[
		{
			"ID": "3",
			"Název CZ": "Areál",
			"Tabulka": "FM_PSP_Site",
			"Primární klíč": "SiteID",
			"Třída": "Alstanet.Fm.Psp.Business.Site, App_Code",
			"Aplikační objekt": "FM.PSP.Site",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5",
			"Název CZ": "Nemovitost",
			"Tabulka": "FM_PSP_Building",
			"Primární klíč": "BuildingID",
			"Třída": "Alstanet.Fm.Psp.Business.Building, App_Code",
			"Aplikační objekt": "FM.PSP.Building",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "6",
			"Název CZ": "Patro",
			"Tabulka": "FM_PSP_Floor",
			"Primární klíč": "FloorID",
			"Třída": "Alstanet.Fm.Psp.Business.Floor, App_Code",
			"Aplikační objekt": "FM.PSP.Floor",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7",
			"Název CZ": "Místnost",
			"Tabulka": "FM_PSP_Room",
			"Primární klíč": "RoomID",
			"Třída": "Alstanet.Fm.Psp.Business.Room, App_Code",
			"Aplikační objekt": "FM.PSP.Room",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "9",
			"Název CZ": "Subjekt",
			"Tabulka": "FM_HR_Subject",
			"Primární klíč": "SubjectID",
			"Třída": "Alstanet.Fm.Hr.Business.Subject, App_Code",
			"Aplikační objekt": "FM.HR.Subject",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "10",
			"Název CZ": "Osoba",
			"Tabulka": "FM_HR_Employee",
			"Primární klíč": "EmployeeID",
			"Třída": "Alstanet.Fm.Hr.Business.Employee, App_Code",
			"Aplikační objekt": "FM.HR.Employee",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "11",
			"Název CZ": "Majetek",
			"Tabulka": "FM_AST_Asset",
			"Primární klíč": "AssetID",
			"Třída": "Alstanet.Fm.Ast.Business.Asset, App_Code",
			"Aplikační objekt": "FM.AST.Asset",
			"Modul": "Majetek"
		},
		{
			"ID": "13",
			"Název CZ": "Technologie",
			"Tabulka": "FM_TCH_Technology",
			"Primární klíč": "TechnologyID",
			"Třída": "Alstanet.Fm.Tch.Business.Technology, App_Code",
			"Aplikační objekt": "FM.TCH.Technology",
			"Modul": "Technologie"
		},
		{
			"ID": "23",
			"Název CZ": "Typ 1",
			"Tabulka": "FM_TCH_Type1",
			"Primární klíč": "Type1ID",
			"Třída": "Alstanet.Fm.Tch.Business.Type1, App_Code",
			"Aplikační objekt": "FM.TCH.Type1",
			"Modul": "Technologie"
		},
		{
			"ID": "24",
			"Název CZ": "Typ 2",
			"Tabulka": "FM_TCH_Type2",
			"Primární klíč": "Type2ID",
			"Třída": "Alstanet.Fm.Tch.Business.Type2, App_Code",
			"Aplikační objekt": "FM.TCH.Type2",
			"Modul": "Technologie"
		},
		{
			"ID": "25",
			"Název CZ": "Typ 3",
			"Tabulka": "FM_TCH_Type3",
			"Primární klíč": "Type3ID",
			"Třída": "Alstanet.Fm.Tch.Business.Type3, App_Code",
			"Aplikační objekt": "FM.TCH.Type3",
			"Modul": "Technologie"
		},
		{
			"ID": "26",
			"Název CZ": "Katalog technologie",
			"Tabulka": "FM_TCH_Catalog",
			"Primární klíč": "CatalogID",
			"Třída": "Alstanet.Fm.Tch.Business.Catalog, App_Code",
			"Aplikační objekt": "FM.TCH.Catalog",
			"Modul": "Technologie"
		},
		{
			"ID": "30",
			"Název CZ": "Pracovní místo",
			"Tabulka": "FM_PSP_Workspace",
			"Primární klíč": "WorkspaceID",
			"Třída": "Alstanet.Fm.Psp.Business.Workspace, App_Code",
			"Aplikační objekt": "FM.PSP.Workspace",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "101",
			"Název CZ": "Prostor",
			"Tabulka": "FM_PSP_Zone",
			"Primární klíč": "ZoneID",
			"Třída": "Alstanet.Fm.Psp.Business.Zone, App_Code",
			"Aplikační objekt": "FM.PSP.Zone",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "106",
			"Název CZ": "Jednotka",
			"Tabulka": "FM_PSP_UnitGroup",
			"Primární klíč": "UnitGroupID",
			"Třída": "Alstanet.Fm.Psp.Business.UnitGroup, App_Code",
			"Aplikační objekt": "FM.PSP.UnitGroup",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "107",
			"Název CZ": "Rekonstrukce",
			"Tabulka": "FM_PSP_Reconstruction",
			"Primární klíč": "ReconstructionID",
			"Třída": "Alstanet.Fm.Psp.Business.Reconstruction, App_Code",
			"Aplikační objekt": "FM.PSP.Reconstruction",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "201",
			"Název CZ": "Požadavek",
			"Tabulka": "FM_MNT_Incident",
			"Primární klíč": "IncidentID",
			"Třída": "Alstanet.Fm.Mnt.Business.Incident, App_Code",
			"Aplikační objekt": "FM.MNT.Incident",
			"Modul": "Údržba"
		},
		{
			"ID": "202",
			"Název CZ": "Plán údržby",
			"Tabulka": "FM_MNT_Plan",
			"Primární klíč": "PlanID",
			"Třída": "Alstanet.Fm.Mnt.Business.Plan, App_Code",
			"Aplikační objekt": "FM.MNT.Plan",
			"Modul": "Údržba"
		},
		{
			"ID": "203",
			"Název CZ": "Šablona plánu údržby",
			"Tabulka": "FM_MNT_PlanTemplate",
			"Primární klíč": "PlanTemplateID",
			"Třída": "Alstanet.Fm.Mnt.Business.PlanTemplate, App_Code",
			"Aplikační objekt": "FM.MNT.PlanTemplate",
			"Modul": "Údržba"
		},
		{
			"ID": "204",
			"Název CZ": "Revizní zpráva",
			"Tabulka": "FM_MNT_Revision",
			"Primární klíč": "RevisionID",
			"Třída": "Alstanet.Fm.Mnt.Business.Revision, App_Code",
			"Aplikační objekt": "FM.MNT.Revision",
			"Modul": "Údržba"
		},
		{
			"ID": "205",
			"Název CZ": "Položka požadavku",
			"Tabulka": "FM_MNT_IncidentItem",
			"Primární klíč": "IncidentItemID",
			"Třída": "Alstanet.Fm.Mnt.Business.IncidentItem, App_Code",
			"Aplikační objekt": "FM.MNT.IncidentItem",
			"Modul": "Údržba"
		},
		{
			"ID": "206",
			"Název CZ": "Položka plánu",
			"Tabulka": "FM_MNT_PlanItem",
			"Primární klíč": "PlanItemID",
			"Třída": "Alstanet.Fm.Mnt.Business.PlanItem, App_Code",
			"Aplikační objekt": "FM.MNT.PlanItem",
			"Modul": "Údržba"
		},
		{
			"ID": "207",
			"Název CZ": "Položka šablony plánu údržby",
			"Tabulka": "FM_MNT_PlanTemplateItem",
			"Primární klíč": "PlanTemplateItemID",
			"Třída": "Alstanet.Fm.Mnt.Business.PlanTemplateItem, App_Code",
			"Aplikační objekt": "FM.MNT.PlanTemplateItem",
			"Modul": "Údržba"
		},
		{
			"ID": "208",
			"Název CZ": "Plánovaný výskyt",
			"Tabulka": "FM_MNT_Occurence",
			"Primární klíč": "OccurenceID",
			"Třída": "Alstanet.Fm.Mnt.Business.Occurence, App_Code",
			"Aplikační objekt": "FM.MNT.Occurence",
			"Modul": "Údržba"
		},
		{
			"ID": "209",
			"Název CZ": "Závažnost",
			"Tabulka": "FM_MNT_IncidentPriority",
			"Primární klíč": "PriorityID",
			"Třída": "Alstanet.Fm.Mnt.Business.IncidentPriority, App_Code",
			"Aplikační objekt": "FM.MNT.IncidentPriority",
			"Modul": "Údržba"
		},
		{
			"ID": "210",
			"Název CZ": "Kategorie",
			"Tabulka": "FM_MNT_IncidentCategory",
			"Primární klíč": "CategoryID",
			"Třída": "Alstanet.Fm.Mnt.Business.IncidentCategory, App_Code",
			"Aplikační objekt": "FM.MNT.IncidentCategory",
			"Modul": "Údržba"
		},
		{
			"ID": "301",
			"Název CZ": "Kniha jízd",
			"Tabulka": "FM_CF_Drivebook",
			"Primární klíč": "DrivebookID",
			"Třída": "Alstanet.Fm.Cf.Business.Drivebook, App_Code",
			"Aplikační objekt": "FM.CF.Drivebook",
			"Modul": "Autopark"
		},
		{
			"ID": "401",
			"Název CZ": "Katalog majetku",
			"Tabulka": "FM_AST_Catalog",
			"Primární klíč": "CatalogID",
			"Třída": "Alstanet.Fm.Ast.Business.Catalog, App_Code",
			"Aplikační objekt": "FM.AST.Catalog",
			"Modul": "Majetek"
		},
		{
			"ID": "501",
			"Název CZ": "Smlouva",
			"Tabulka": "FM_CM_Contract",
			"Primární klíč": "ContractID",
			"Třída": "Alstanet.Fm.Cm.Business.Contract, App_Code",
			"Aplikační objekt": "FM.CM.Contract",
			"Modul": "Smlouvy"
		},
		{
			"ID": "502",
			"Název CZ": "Položka smlouvy",
			"Tabulka": "FM_CM_ContractItem",
			"Primární klíč": "ContractItemID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractItem, App_Code",
			"Aplikační objekt": "FM.CM.ContractItem",
			"Modul": "Smlouvy"
		},
		{
			"ID": "503",
			"Název CZ": "Dodatek",
			"Tabulka": "FM_CM_ContractAmendment",
			"Primární klíč": "ContractAmendmentID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractAmendment, App_Code",
			"Aplikační objekt": "FM.CM.ContractAmendment",
			"Modul": "Smlouvy"
		},
		{
			"ID": "504",
			"Název CZ": "Záruka",
			"Tabulka": "FM_CM_ContractGuarantee",
			"Primární klíč": "ContractGuaranteeID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractGuarantee, App_Code",
			"Aplikační objekt": "FM.CM.ContractGuarantee",
			"Modul": "Smlouvy"
		},
		{
			"ID": "505",
			"Název CZ": "Fakturační předpis",
			"Tabulka": "FM_CM_InvoicePrescript",
			"Primární klíč": "InvoicePrescriptID",
			"Třída": "Alstanet.Fm.Cm.Business.InvoicePrescript, App_Code",
			"Aplikační objekt": "FM.CM.InvoicePrescript",
			"Modul": "Smlouvy"
		},
		{
			"ID": "506",
			"Název CZ": "Předmět nájmu",
			"Tabulka": "FM_CM_LeaseSubject",
			"Primární klíč": "LeaseSubjectID",
			"Třída": "Alstanet.Fm.Cm.Business.LeaseSubject, App_Code",
			"Aplikační objekt": "FM.CM.LeaseSubject",
			"Modul": "Smlouvy"
		},
		{
			"ID": "507",
			"Název CZ": "Cena služby",
			"Tabulka": "FM_CM_ContractItemPrice",
			"Primární klíč": "ContractItemPriceID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractItemPrice, App_Code",
			"Aplikační objekt": "FM.CM.ContractItemPrice",
			"Modul": "Smlouvy"
		},
		{
			"ID": "508",
			"Název CZ": "Index",
			"Tabulka": "FM_CM_PriceIndex",
			"Primární klíč": "PriceIndexID",
			"Třída": "Alstanet.Fm.Cm.Business.PriceIndex, App_Code",
			"Aplikační objekt": "FM.CM.PriceIndex",
			"Modul": "Smlouvy"
		},
		{
			"ID": "801",
			"Název CZ": "Položka plánu",
			"Tabulka": "FM_CO_PlanItem",
			"Primární klíč": "PlanItemID",
			"Třída": "Alstanet.Fm.Co.Business.PlanItem, App_Code",
			"Aplikační objekt": "FM.CO.PlanItem",
			"Modul": "Controlling"
		},
		{
			"ID": "802",
			"Název CZ": "Stav čerpání",
			"Tabulka": "FM_CO_PlanUtilization",
			"Primární klíč": "PlanUtilizationID",
			"Třída": "Alstanet.Fm.Co.Business.PlanUtilization, App_Code",
			"Aplikační objekt": "FM.CO.PlanUtilization",
			"Modul": "Controlling"
		},
		{
			"ID": "803",
			"Název CZ": "Dohadní položka",
			"Tabulka": "FM_CO_ConjecturalItem",
			"Primární klíč": "ConjecturalItemID",
			"Třída": "Alstanet.Fm.Co.Business.ConjecturalItem, App_Code",
			"Aplikační objekt": "FM.CO.ConjecturalItem",
			"Modul": "Controlling"
		},
		{
			"ID": "1801",
			"Název CZ": "Odběrné místo",
			"Tabulka": "FM_ENG_Place",
			"Primární klíč": "PlaceID",
			"Třída": "Alstanet.Fm.Eng.Business.Place, App_Code",
			"Aplikační objekt": "FM.ENG.Place",
			"Modul": "Energie"
		},
		{
			"ID": "1802",
			"Název CZ": "Měřidlo",
			"Tabulka": "FM_ENG_Gauge",
			"Primární klíč": "GaugeID",
			"Třída": "Alstanet.Fm.Eng.Business.Gauge, App_Code",
			"Aplikační objekt": "FM.ENG.Gauge",
			"Modul": "Energie"
		},
		{
			"ID": "1803",
			"Název CZ": "Odečet",
			"Tabulka": "FM_ENG_Reading",
			"Primární klíč": "ReadingID",
			"Třída": "Alstanet.Fm.Eng.Business.Reading, App_Code",
			"Aplikační objekt": "FM.ENG.Reading",
			"Modul": "Energie"
		},
		{
			"ID": "1804",
			"Název CZ": "Řada odečtů",
			"Tabulka": "FM_ENG_GaugeRate",
			"Primární klíč": "GaugeRateID",
			"Třída": "Alstanet.Fm.Eng.Business.GaugeRate, App_Code",
			"Aplikační objekt": "FM.ENG.GaugeRate",
			"Modul": "Energie"
		},
		{
			"ID": "1901",
			"Název CZ": "Faktura",
			"Tabulka": "FM_IVO_Invoice",
			"Primární klíč": "InvoiceID",
			"Třída": "Alstanet.Fm.Ivo.Business.Invoice, App_Code",
			"Aplikační objekt": "FM.IVO.Invoice",
			"Modul": "Faktury"
		},
		{
			"ID": "1902",
			"Název CZ": "Položka faktury",
			"Tabulka": "FM_IVO_InvoiceItem",
			"Primární klíč": "InvoiceItemID",
			"Třída": "Alstanet.Fm.Ivo.Business.InvoiceItem, App_Code",
			"Aplikační objekt": "FM.IVO.InvoiceItem",
			"Modul": "Faktury"
		},
		{
			"ID": "1903",
			"Název CZ": "Rozpadový klíč",
			"Tabulka": "FM_IVO_DistributionKey",
			"Primární klíč": "DistributionKeyID",
			"Třída": "Alstanet.Fm.Ivo.Business.DistributionKey, App_Code",
			"Aplikační objekt": "FM.IVO.DistributionKey",
			"Modul": "Faktury"
		},
		{
			"ID": "2001",
			"Název CZ": "Činnost",
			"Tabulka": "FM_FI_Activity",
			"Primární klíč": "ActivityID",
			"Třída": "Alstanet.Fm.Fi.Business.Activity, App_Code",
			"Aplikační objekt": "FM.FI.Activity",
			"Modul": "Ekonomika"
		},
		{
			"ID": "2002",
			"Název CZ": "Kurzovní lístek",
			"Tabulka": "FM_FI_ExchangeRate",
			"Primární klíč": "ExchangeRateID",
			"Třída": "Alstanet.Fm.Fi.Business.ExchangeRate, App_Code",
			"Aplikační objekt": "FM.FI.ExchangeRate",
			"Modul": "Ekonomika"
		},
		{
			"ID": "2201",
			"Název CZ": "Článek",
			"Tabulka": "FM_CMS_Article",
			"Primární klíč": "ArticleID",
			"Třída": "Alstanet.Fm.Cms.Business.Article, App_Code",
			"Aplikační objekt": "FM.CMS.Article",
			"Modul": "Portál"
		},
		{
			"ID": "2301",
			"Název CZ": "Schvalování",
			"Tabulka": "FM_WF_Approve",
			"Primární klíč": "ApproveID",
			"Třída": "Alstanet.Fm.Wf.Business.Approve, App_Code",
			"Aplikační objekt": "FM.WF.Approve",
			"Modul": "Workflow"
		},
		{
			"ID": "2302",
			"Název CZ": "Schvalovatel",
			"Tabulka": "FM_WF_Approver",
			"Primární klíč": "ApproverID",
			"Třída": "Alstanet.Fm.Wf.Business.Approver, App_Code",
			"Aplikační objekt": "FM.WF.Approver",
			"Modul": "Workflow"
		},
		{
			"ID": "31528",
			"Název CZ": "Modelová řada",
			"Tabulka": "FM_CF_ModelSeries",
			"Primární klíč": "ModelSeriesID",
			"Třída": "Alstanet.Fm.Cf.Business.ModelSeries, App_Code",
			"Aplikační objekt": "FM.CF.ModelSeries",
			"Modul": "Autopark"
		},
		{
			"ID": "96217",
			"Název CZ": "Obrat - období",
			"Tabulka": "FM_CM_TurnoverPeriod",
			"Primární klíč": "TurnoverPeriodID",
			"Třída": "Alstanet.Fm.Cm.Business.TurnoverPeriod, App_Code",
			"Aplikační objekt": "FM.CM.TurnoverPeriod",
			"Modul": "Smlouvy"
		},
		{
			"ID": "116279",
			"Název CZ": "Části povrchu podlahy",
			"Tabulka": "FM_PSP_FloorPart",
			"Primární klíč": "FloorPartID",
			"Třída": "Alstanet.Fm.Psp.Business.FloorPart, App_Code",
			"Aplikační objekt": "FM.PSP.FloorPart",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "120156",
			"Název CZ": "Spotřeba CR360 - Typ dat",
			"Tabulka": "FM_ENG_ACECR360DataType",
			"Primární klíč": "ACECR360DataTypeID",
			"Třída": "Alstanet.Fm.Eng.Business.ACECR360DataType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACECR360DataType",
			"Modul": "Energie"
		},
		{
			"ID": "158046",
			"Název CZ": "Typy pojištění - likvidace",
			"Tabulka": "FM_CF_LiquidationInsuranceType",
			"Primární klíč": "LiquidationInsuranceTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.LiquidationInsuranceType, App_Code",
			"Aplikační objekt": "FM.CF.LiquidationInsuranceType",
			"Modul": "Autopark"
		},
		{
			"ID": "174639",
			"Název CZ": "Oblast",
			"Tabulka": "FM_PSP_Region",
			"Primární klíč": "RegionID",
			"Třída": "Alstanet.Fm.Psp.Business.Region, App_Code",
			"Aplikační objekt": "FM.PSP.Region",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "206224",
			"Název CZ": "Podpis",
			"Tabulka": "AMC_Signature",
			"Primární klíč": "SignatureID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Signature, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Signature",
			"Modul": "Administrace"
		},
		{
			"ID": "210719",
			"Název CZ": "Kvantifikace služby",
			"Tabulka": "FM_CM_ServiceQuantification",
			"Primární klíč": "ServiceQuantificationID",
			"Třída": "Alstanet.Fm.Cm.Business.ServiceQuantification, App_Code",
			"Aplikační objekt": "FM.CM.ServiceQuantification",
			"Modul": "Smlouvy"
		},
		{
			"ID": "224673",
			"Název CZ": "Nastavení repasportizace",
			"Tabulka": "FM_CAD_RepasportizationDefinition",
			"Primární klíč": "RepasportizationDefinitionID",
			"Třída": "Alstanet.Fm.Cad.Business.RepasportizationDefinition, App_Code",
			"Aplikační objekt": "FM.CAD.RepasportizationDefinition",
			"Modul": "Vizualizace"
		},
		{
			"ID": "229657",
			"Název CZ": "Rozpad podle",
			"Tabulka": "FM_IVO_DistributionBy",
			"Primární klíč": "DistributionByID",
			"Třída": "Alstanet.Fm.Ivo.Business.DistributionBy, App_Code",
			"Aplikační objekt": "FM.IVO.DistributionBy",
			"Modul": "Faktury"
		},
		{
			"ID": "233928",
			"Název CZ": "Perioda refakturace",
			"Tabulka": "FM_IVO_ReinvoicePeriod",
			"Primární klíč": "ReinvoicePeriodID",
			"Třída": "Alstanet.Fm.Ivo.Business.ReinvoicePeriod, App_Code",
			"Aplikační objekt": "FM.IVO.ReinvoicePeriod",
			"Modul": "Faktury"
		},
		{
			"ID": "250878",
			"Název CZ": "Vozidlo",
			"Tabulka": "FM_CF_Car",
			"Primární klíč": "CarID",
			"Třída": "Alstanet.Fm.Cf.Business.Car, App_Code",
			"Aplikační objekt": "FM.CF.Car",
			"Modul": "Autopark"
		},
		{
			"ID": "259047",
			"Název CZ": "Souborová šablona",
			"Tabulka": "AMC_DocumentTemplate",
			"Primární klíč": "DocumentTemplateID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DocumentTemplate, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DocumentTemplate",
			"Modul": "Administrace"
		},
		{
			"ID": "302887",
			"Název CZ": "Určení přidělení vozidla",
			"Tabulka": "FM_CF_AssignDesignation",
			"Primární klíč": "AssignDesignationID",
			"Třída": "Alstanet.Fm.Cf.Business.AssignDesignation, App_Code",
			"Aplikační objekt": "FM.CF.AssignDesignation",
			"Modul": "Autopark"
		},
		{
			"ID": "319407",
			"Název CZ": "Sada atributů",
			"Tabulka": "AMC_AttributeSet",
			"Primární klíč": "AttributeSetID",
			"Třída": "Alstanet.Library.Web.AMC.Business.AttributeSet, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.AttributeSet",
			"Modul": "Administrace"
		},
		{
			"ID": "324786",
			"Název CZ": "GPS Identifikátory vozidel",
			"Tabulka": "FM_CF_GPSProviderCarCode",
			"Primární klíč": "GPSProviderCarCodeID",
			"Třída": "Alstanet.Fm.Cf.Business.GPSProviderCarCode, App_Code",
			"Aplikační objekt": "FM.CF.GPSProviderCarCode",
			"Modul": "Autopark"
		},
		{
			"ID": "326888",
			"Název CZ": "Klič na zámku",
			"Tabulka": "FM_KCM_LockInsert_Key",
			"Primární klíč": "LockInsert_KeyID",
			"Třída": "Alstanet.Fm.Kcm.Business.LockInsert_Key, App_Code",
			"Aplikační objekt": "FM.KCM.LockInsert_Key",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "341950",
			"Název CZ": "Typ zdroje souboru",
			"Tabulka": "AMC_DocumentInputType",
			"Primární klíč": "DocumentInputTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DocumentInputType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DocumentInputType",
			"Modul": "Administrace"
		},
		{
			"ID": "343142",
			"Název CZ": "Historie smlouvy",
			"Tabulka": "FM_CM_ACEContractLog",
			"Primární klíč": "ACEContractLogID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEContractLog, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEContractLog",
			"Modul": "Smlouvy"
		},
		{
			"ID": "358344",
			"Název CZ": "Stav nového majetku",
			"Tabulka": "FM_INV_NewAssetStatus",
			"Primární klíč": "NewAssetStatusID",
			"Třída": "Alstanet.Fm.Inv.Business.NewAssetStatus, App_Code",
			"Aplikační objekt": "FM.INV.NewAssetStatus",
			"Modul": "Inventury"
		},
		{
			"ID": "367236",
			"Název CZ": "Email",
			"Tabulka": "AMC_EmailLogEmail",
			"Primární klíč": "EmailLogEmailID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailLogEmail, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailLogEmail",
			"Modul": "Administrace"
		},
		{
			"ID": "380433",
			"Název CZ": "Distribuční sazba",
			"Tabulka": "FM_CF_DistributionRate",
			"Primární klíč": "DistributionRateID",
			"Třída": "Alstanet.Fm.Cf.Business.DistributionRate, App_Code",
			"Aplikační objekt": "FM.CF.DistributionRate",
			"Modul": "Autopark"
		},
		{
			"ID": "441019",
			"Název CZ": "Stav schvalovatele",
			"Tabulka": "FM_WF_ApproverStatus",
			"Primární klíč": "ApproverStatusID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproverStatus, App_Code",
			"Aplikační objekt": "FM.WF.ApproverStatus",
			"Modul": "Workflow"
		},
		{
			"ID": "463591",
			"Název CZ": "ESRS datový typ",
			"Tabulka": "ACM_ESG_ACEESRSDataType",
			"Primární klíč": "ACEESRSDataTypeID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEESRSDataType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEESRSDataType",
			"Modul": "ESG"
		},
		{
			"ID": "476359",
			"Název CZ": "Měsíční uzávěrka",
			"Tabulka": "FM_CF_MonthlyClosing",
			"Primární klíč": "MonthlyClosingID",
			"Třída": "Alstanet.Fm.Cf.Business.MonthlyClosing, App_Code",
			"Aplikační objekt": "FM.CF.MonthlyClosing",
			"Modul": "Autopark"
		},
		{
			"ID": "487950",
			"Název CZ": "Umístění místnosti",
			"Tabulka": "FM_PSP_RoomPosition",
			"Primární klíč": "RoomPositionID",
			"Třída": "Alstanet.Fm.Psp.Business.RoomPosition, App_Code",
			"Aplikační objekt": "FM.PSP.RoomPosition",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "510592",
			"Název CZ": "Položka objednávky",
			"Tabulka": "FM_ORD_OrderItem",
			"Primární klíč": "OrderItemID",
			"Třída": "Alstanet.Fm.Ord.Business.OrderItem, App_Code",
			"Aplikační objekt": "FM.ORD.OrderItem",
			"Modul": "Objednávky"
		},
		{
			"ID": "516808",
			"Název CZ": "Skupina akcí",
			"Tabulka": "AMC_CustActionGroup",
			"Primární klíč": "CustActionGroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustActionGroup, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustActionGroup",
			"Modul": "Administrace"
		},
		{
			"ID": "524753",
			"Název CZ": "Notifikace",
			"Tabulka": "AMC_Notification",
			"Primární klíč": "NotificationID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Notification, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Notification",
			"Modul": "Administrace"
		},
		{
			"ID": "527369",
			"Název CZ": "Stav schvalování",
			"Tabulka": "FM_WF_ApproveStatus",
			"Primární klíč": "ApproveStatusID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproveStatus, App_Code",
			"Aplikační objekt": "FM.WF.ApproveStatus",
			"Modul": "Workflow"
		},
		{
			"ID": "529271",
			"Název CZ": "Přidělení lockeru",
			"Tabulka": "ACM_LOC_ACELockerAssign",
			"Primární klíč": "ACELockerAssignID",
			"Třída": "Alstanet.Acm.Loc.Business.ACELockerAssign, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.LOC.ACELockerAssign",
			"Modul": "Lockers"
		},
		{
			"ID": "545362",
			"Název CZ": "Energetický mix",
			"Tabulka": "ACM_ESG_ACEEnergyMix",
			"Primární klíč": "ACEEnergyMixID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEnergyMix, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEnergyMix",
			"Modul": "ESG"
		},
		{
			"ID": "605413",
			"Název CZ": "Ceník silniční daně",
			"Tabulka": "FM_CF_RoadTaxPriceList",
			"Primární klíč": "RoadTaxPriceListID",
			"Třída": "Alstanet.Fm.Cf.Business.RoadTaxPriceList, App_Code",
			"Aplikační objekt": "FM.CF.RoadTaxPriceList",
			"Modul": "Autopark"
		},
		{
			"ID": "627632",
			"Název CZ": "Pohon",
			"Tabulka": "FM_CF_Propulsion",
			"Primární klíč": "PropulsionID",
			"Třída": "Alstanet.Fm.Cf.Business.Propulsion, App_Code",
			"Aplikační objekt": "FM.CF.Propulsion",
			"Modul": "Autopark"
		},
		{
			"ID": "658628",
			"Název CZ": "Druh dokumentu",
			"Tabulka": "FM_CM_ContractKind",
			"Primární klíč": "ContractKindID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractKind, App_Code",
			"Aplikační objekt": "FM.CM.ContractKind",
			"Modul": "Smlouvy"
		},
		{
			"ID": "706844",
			"Název CZ": "Typ nebezpečí",
			"Tabulka": "FM_PSP_RiskType",
			"Primární klíč": "RiskTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.RiskType, App_Code",
			"Aplikační objekt": "FM.PSP.RiskType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "713567",
			"Název CZ": "Palivo",
			"Tabulka": "FM_CF_Fuel",
			"Primární klíč": "FuelID",
			"Třída": "Alstanet.Fm.Cf.Business.Fuel, App_Code",
			"Aplikační objekt": "FM.CF.Fuel",
			"Modul": "Autopark"
		},
		{
			"ID": "723200",
			"Název CZ": "Vyřazovaný majetek",
			"Tabulka": "FM_AST_DisposalAsset",
			"Primární klíč": "DisposalAssetID",
			"Třída": "Alstanet.Fm.Ast.Business.DisposalAsset, App_Code",
			"Aplikační objekt": "FM.AST.DisposalAsset",
			"Modul": "Majetek"
		},
		{
			"ID": "757477",
			"Název CZ": "List vlastnictví",
			"Tabulka": "FM_CDS_OwnershipList",
			"Primární klíč": "OwnershipListID",
			"Třída": "Alstanet.Fm.Cds.Business.OwnershipList, App_Code",
			"Aplikační objekt": "FM.CDS.OwnershipList",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "764364",
			"Název CZ": "SPP prvek",
			"Tabulka": "FM_FI_SPPElement",
			"Primární klíč": "SPPElementID",
			"Třída": "Alstanet.Fm.Fi.Business.SPPElement, App_Code",
			"Aplikační objekt": "FM.FI.SPPElement",
			"Modul": "Ekonomika"
		},
		{
			"ID": "783495",
			"Název CZ": "Sazba DPH",
			"Tabulka": "FM_FI_VatRate",
			"Primární klíč": "VatRateID",
			"Třída": "Alstanet.Fm.Fi.Business.VatRate, App_Code",
			"Aplikační objekt": "FM.FI.VatRate",
			"Modul": "Ekonomika"
		},
		{
			"ID": "815071",
			"Název CZ": "Účetní okruh",
			"Tabulka": "FM_AST_AccountingArea",
			"Primární klíč": "AccountingAreaID",
			"Třída": "Alstanet.Fm.Ast.Business.AccountingArea, App_Code",
			"Aplikační objekt": "FM.AST.AccountingArea",
			"Modul": "Majetek"
		},
		{
			"ID": "823855",
			"Název CZ": "Log změn",
			"Tabulka": "AMC_EventLogItem",
			"Primární klíč": "EventLogItemID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogItem, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogItem",
			"Modul": "Administrace"
		},
		{
			"ID": "854027",
			"Název CZ": "Typ publikačního objektu",
			"Tabulka": "FM_CMS_PublObjectType",
			"Primární klíč": "PublObjectTypeID",
			"Třída": "Alstanet.Fm.Cms.Business.PublObjectType, App_Code",
			"Aplikační objekt": "FM.CMS.PublObjectType",
			"Modul": "Portál"
		},
		{
			"ID": "868778",
			"Název CZ": "Typ plánu",
			"Tabulka": "FM_MNT_PlanType",
			"Primární klíč": "PlanTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.PlanType, App_Code",
			"Aplikační objekt": "FM.MNT.PlanType",
			"Modul": "Údržba"
		},
		{
			"ID": "887342",
			"Název CZ": "Statistická zakázka",
			"Tabulka": "FM_FI_StatisticOrder",
			"Primární klíč": "StatisticOrderID",
			"Třída": "Alstanet.Fm.Fi.Business.StatisticOrder, App_Code",
			"Aplikační objekt": "FM.FI.StatisticOrder",
			"Modul": "Ekonomika"
		},
		{
			"ID": "896332",
			"Název CZ": "Měna",
			"Tabulka": "FM_FI_Currency",
			"Primární klíč": "CurrencyID",
			"Třída": "Alstanet.Fm.Fi.Business.Currency, App_Code",
			"Aplikační objekt": "FM.FI.Currency",
			"Modul": "Ekonomika"
		},
		{
			"ID": "918544",
			"Název CZ": "Podtyp dokumentu",
			"Tabulka": "FM_MNT_RevisionDocType",
			"Primární klíč": "RevisionDocTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.RevisionDocType, App_Code",
			"Aplikační objekt": "FM.MNT.RevisionDocType",
			"Modul": "Údržba"
		},
		{
			"ID": "931150",
			"Název CZ": "Druh dokumentu smlouvy",
			"Tabulka": "FM_CM_ACEContractDocumentKind",
			"Primární klíč": "ACEContractDocumentKindID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEContractDocumentKind, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEContractDocumentKind",
			"Modul": "Smlouvy"
		},
		{
			"ID": "961673",
			"Název CZ": "Typ 2FA",
			"Tabulka": "AMC_TwoFactorType",
			"Primární klíč": "TwoFactorTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.TwoFactorType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.TwoFactorType",
			"Modul": "Administrace"
		},
		{
			"ID": "1017884",
			"Název CZ": "Vybavení vozidla",
			"Tabulka": "FM_CF_CarEquipment",
			"Primární klíč": "CarEquipmentID",
			"Třída": "Alstanet.Fm.Cf.Business.CarEquipment, App_Code",
			"Aplikační objekt": "FM.CF.CarEquipment",
			"Modul": "Autopark"
		},
		{
			"ID": "1041195",
			"Název CZ": "Schvalování vyřazení",
			"Tabulka": "FM_AST_DisposalApprove",
			"Primární klíč": "DisposalApproveID",
			"Třída": "Alstanet.Fm.Ast.Business.DisposalApprove, App_Code",
			"Aplikační objekt": "FM.AST.DisposalApprove",
			"Modul": "Majetek"
		},
		{
			"ID": "1063732",
			"Název CZ": "Typ prostoru",
			"Tabulka": "FM_PSP_ZoneType",
			"Primární klíč": "ZoneTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.ZoneType, App_Code",
			"Aplikační objekt": "FM.PSP.ZoneType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "1075029",
			"Název CZ": "Čerpání PHM",
			"Tabulka": "FM_CF_OperationRefuel",
			"Primární klíč": "OperationRefuelID",
			"Třída": "Alstanet.Fm.Cf.Business.OperationRefuel, App_Code",
			"Aplikační objekt": "FM.CF.OperationRefuel",
			"Modul": "Autopark"
		},
		{
			"ID": "1093163",
			"Název CZ": "Žádost o přístup",
			"Tabulka": "FM_KCM_AccessRequest",
			"Primární klíč": "AccessRequestID",
			"Třída": "Alstanet.Fm.Kcm.Business.AccessRequest, App_Code",
			"Aplikační objekt": "FM.KCM.AccessRequest",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "1107863",
			"Název CZ": "Role",
			"Tabulka": "FM_TCH_Role",
			"Primární klíč": "RoleID",
			"Třída": "Alstanet.Fm.Tch.Business.Role, App_Code",
			"Aplikační objekt": "FM.TCH.Role",
			"Modul": "Technologie"
		},
		{
			"ID": "1128653",
			"Název CZ": "Typ break option",
			"Tabulka": "FM_CM_BreakOptionType",
			"Primární klíč": "BreakOptionTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.BreakOptionType, App_Code",
			"Aplikační objekt": "FM.CM.BreakOptionType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "1156507",
			"Název CZ": "Editace článku",
			"Tabulka": "AMC_HelpArticleEdit",
			"Primární klíč": "HelpArticleEditID",
			"Třída": "Alstanet.Library.Web.AMC.Business.HelpArticleEdit, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.HelpArticleEdit",
			"Modul": "Administrace"
		},
		{
			"ID": "1171494",
			"Název CZ": "Přechod stavu",
			"Tabulka": "FM_WF_StatusTransition",
			"Primární klíč": "StatusTransitionID",
			"Třída": "Alstanet.Fm.Wf.Business.StatusTransition, App_Code",
			"Aplikační objekt": "FM.WF.StatusTransition",
			"Modul": "Workflow"
		},
		{
			"ID": "1219892",
			"Název CZ": "Časový rámec služby",
			"Tabulka": "FM_CM_ServicePeriod",
			"Primární klíč": "ServicePeriodID",
			"Třída": "Alstanet.Fm.Cm.Business.ServicePeriod, App_Code",
			"Aplikační objekt": "FM.CM.ServicePeriod",
			"Modul": "Smlouvy"
		},
		{
			"ID": "1251533",
			"Název CZ": "Úkol",
			"Tabulka": "FM_HR_Task",
			"Primární klíč": "TaskID",
			"Třída": "Alstanet.Fm.Hr.Business.Task, App_Code",
			"Aplikační objekt": "FM.HR.Task",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "1256901",
			"Název CZ": "SQL log",
			"Tabulka": "AMC_CommandLog",
			"Primární klíč": "CommandLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CommandLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CommandLog",
			"Modul": "Administrace"
		},
		{
			"ID": "1257145",
			"Název CZ": "Log přechodu",
			"Tabulka": "FM_WF_StatusLog",
			"Primární klíč": "StatusLogID",
			"Třída": "Alstanet.Fm.Wf.Business.StatusLog, App_Code",
			"Aplikační objekt": "FM.WF.StatusLog",
			"Modul": "Workflow"
		},
		{
			"ID": "1293790",
			"Název CZ": "Typ parkovacího místa",
			"Tabulka": "FM_CF_ParkPlaceType",
			"Primární klíč": "ParkPlaceTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.ParkPlaceType, App_Code",
			"Aplikační objekt": "FM.CF.ParkPlaceType",
			"Modul": "Autopark"
		},
		{
			"ID": "1295815",
			"Název CZ": "Výkres",
			"Tabulka": "FM_CAD_Plan",
			"Primární klíč": "PlanID",
			"Třída": "Alstanet.Fm.Cad.Business.Plan, App_Code",
			"Aplikační objekt": "FM.CAD.Plan",
			"Modul": "Vizualizace"
		},
		{
			"ID": "1308356",
			"Název CZ": "Typ překročení",
			"Tabulka": "FM_WF_KpiTimeExceedType",
			"Primární klíč": "KpiTimeExceedTypeID",
			"Třída": "Alstanet.Fm.Wf.Business.KpiTimeExceedType, App_Code",
			"Aplikační objekt": "FM.WF.KpiTimeExceedType",
			"Modul": "Workflow"
		},
		{
			"ID": "1315731",
			"Název CZ": "Session",
			"Tabulka": "AMC_SessionData",
			"Primární klíč": "SessionDataID",
			"Třída": "Alstanet.Library.Web.AMC.Business.SessionData, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.SessionData",
			"Modul": "Administrace"
		},
		{
			"ID": "1322094",
			"Název CZ": "Uživatelský modul",
			"Tabulka": "AMC_CustModule",
			"Primární klíč": "CustModuleID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustModule, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustModule",
			"Modul": "Administrace"
		},
		{
			"ID": "1326496",
			"Název CZ": "Typ akce",
			"Tabulka": "FM_INV_ActionType",
			"Primární klíč": "ActionTypeID",
			"Třída": "Alstanet.Fm.Inv.Business.ActionType, App_Code",
			"Aplikační objekt": "FM.INV.ActionType",
			"Modul": "Inventury"
		},
		{
			"ID": "1340656",
			"Název CZ": "Druh prostor",
			"Tabulka": "FM_CM_ContractType",
			"Primární klíč": "ContractTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractType, App_Code",
			"Aplikační objekt": "FM.CM.ContractType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "1368456",
			"Název CZ": "Typ nádoby",
			"Tabulka": "FM_PSP_ContainerType",
			"Primární klíč": "ContainerTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.ContainerType, App_Code",
			"Aplikační objekt": "FM.PSP.ContainerType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "1378072",
			"Název CZ": "Úkon a zdroj",
			"Tabulka": "FM_FI_Operation_Source",
			"Primární klíč": "Operation_SourceID",
			"Třída": "Alstanet.Fm.Fi.Business.Operation_Source, App_Code",
			"Aplikační objekt": "FM.FI.Operation_Source",
			"Modul": "Ekonomika"
		},
		{
			"ID": "1387879",
			"Název CZ": "Typ transformace",
			"Tabulka": "AMC_DataTransformType",
			"Primární klíč": "DataTransformTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DataTransformType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DataTransformType",
			"Modul": "Administrace"
		},
		{
			"ID": "1393288",
			"Název CZ": "Publikační objekt",
			"Tabulka": "FM_CMS_PublObject",
			"Primární klíč": "PublObjectID",
			"Třída": "Alstanet.Fm.Cms.Business.PublObject, App_Code",
			"Aplikační objekt": "FM.CMS.PublObject",
			"Modul": "Portál"
		},
		{
			"ID": "1419612",
			"Název CZ": "Uživatelský parametr",
			"Tabulka": "AMC_CustParam",
			"Primární klíč": "CustParamID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustParam, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustParam",
			"Modul": "Administrace"
		},
		{
			"ID": "1429816",
			"Název CZ": "Typ opakování",
			"Tabulka": "AMC_ScheduleFreqType",
			"Primární klíč": "ScheduleFreqTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ScheduleFreqType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ScheduleFreqType",
			"Modul": "Administrace"
		},
		{
			"ID": "1473061",
			"Název CZ": "Skupina typů události",
			"Tabulka": "AMC_CustActionEventTypeGroup",
			"Primární klíč": "CustActionEventTypeGroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustActionEventTypeGroup, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustActionEventTypeGroup",
			"Modul": "Administrace"
		},
		{
			"ID": "1502585",
			"Název CZ": "Typ článku",
			"Tabulka": "FM_CMS_ArticleType",
			"Primární klíč": "ArticleTypeID",
			"Třída": "Alstanet.Fm.Cms.Business.ArticleType, App_Code",
			"Aplikační objekt": "FM.CMS.ArticleType",
			"Modul": "Portál"
		},
		{
			"ID": "1514026",
			"Název CZ": "Uživatel CR360",
			"Tabulka": "FM_HR_ACEUserCR360",
			"Primární klíč": "ACEUserCR360ID",
			"Třída": "Alstanet.Fm.Hr.Business.ACEUserCR360, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.HR.ACEUserCR360",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "1524541",
			"Název CZ": "Typ dohadné položky",
			"Tabulka": "FM_IVO_ConjecturalItemType",
			"Primární klíč": "ConjecturalItemTypeID",
			"Třída": "Alstanet.Fm.Ivo.Business.ConjecturalItemType, App_Code",
			"Aplikační objekt": "FM.IVO.ConjecturalItemType",
			"Modul": "Faktury"
		},
		{
			"ID": "1562673",
			"Název CZ": "Spotřeba",
			"Tabulka": "FM_ENG_ReadingConsumption",
			"Primární klíč": "ReadingConsumptionID",
			"Třída": "Alstanet.Fm.Eng.Business.ReadingConsumption, App_Code",
			"Aplikační objekt": "FM.ENG.ReadingConsumption",
			"Modul": "Energie"
		},
		{
			"ID": "1569003",
			"Název CZ": "Katastální jednotka",
			"Tabulka": "FM_CDS_UnitGroup",
			"Primární klíč": "UnitGroupID",
			"Třída": "Alstanet.Fm.Cds.Business.UnitGroup, App_Code",
			"Aplikační objekt": "FM.CDS.UnitGroup",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "1569256",
			"Název CZ": "Povrch stěn",
			"Tabulka": "FM_PSP_WallSurface",
			"Primární klíč": "WallSurfaceID",
			"Třída": "Alstanet.Fm.Psp.Business.WallSurface, App_Code",
			"Aplikační objekt": "FM.PSP.WallSurface",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "1591405",
			"Název CZ": "Vrstva",
			"Tabulka": "FM_CAD_Layer",
			"Primární klíč": "LayerID",
			"Třída": "Alstanet.Fm.Cad.Business.Layer, App_Code",
			"Aplikační objekt": "FM.CAD.Layer",
			"Modul": "Vizualizace"
		},
		{
			"ID": "1687934",
			"Název CZ": "Typ offsetu",
			"Tabulka": "ACM_ESG_ACEEmissionOffsetType",
			"Primární klíč": "ACEEmissionOffsetTypeID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEmissionOffsetType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEmissionOffsetType",
			"Modul": "ESG"
		},
		{
			"ID": "1710004",
			"Název CZ": "GPS lokátor",
			"Tabulka": "FM_CF_GPSLocator",
			"Primární klíč": "GPSLocatorID",
			"Třída": "Alstanet.Fm.Cf.Business.GPSLocator, App_Code",
			"Aplikační objekt": "FM.CF.GPSLocator",
			"Modul": "Autopark"
		},
		{
			"ID": "1717404",
			"Název CZ": "Třída",
			"Tabulka": "FM_ENG_PlaceClass",
			"Primární klíč": "PlaceClassID",
			"Třída": "Alstanet.Fm.Eng.Business.PlaceClass, App_Code",
			"Aplikační objekt": "FM.ENG.PlaceClass",
			"Modul": "Energie"
		},
		{
			"ID": "1735379",
			"Název CZ": "OrgUnit",
			"Tabulka": "FM_HR_ACEOrgUnit",
			"Primární klíč": "ACEOrgUnitID",
			"Třída": "Alstanet.Fm.Hr.Business.ACEOrgUnit, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.HR.ACEOrgUnit",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "1748996",
			"Název CZ": "Formát pobočky",
			"Tabulka": "FM_PSP_ACEBranchFormat",
			"Primární klíč": "ACEBranchFormatID",
			"Třída": "Alstanet.Fm.Psp.Business.ACEBranchFormat, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACEBranchFormat",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "1764146",
			"Název CZ": "Viník nehody",
			"Tabulka": "FM_CF_IncidentReportCulprit",
			"Primární klíč": "IncidentReportCulpritID",
			"Třída": "Alstanet.Fm.Cf.Business.IncidentReportCulprit, App_Code",
			"Aplikační objekt": "FM.CF.IncidentReportCulprit",
			"Modul": "Autopark"
		},
		{
			"ID": "1765501",
			"Název CZ": "Uživatelský atribut",
			"Tabulka": "AMC_CustProperty",
			"Primární klíč": "CustPropertyID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustProperty, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustProperty",
			"Modul": "Administrace"
		},
		{
			"ID": "1789179",
			"Název CZ": "Jednání",
			"Tabulka": "FM_HR_Meeting",
			"Primární klíč": "MeetingID",
			"Třída": "Alstanet.Fm.Hr.Business.Meeting, App_Code",
			"Aplikační objekt": "FM.HR.Meeting",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "1789421",
			"Název CZ": "Země",
			"Tabulka": "FM_PSP_Country",
			"Primární klíč": "CountryID",
			"Třída": "Alstanet.Fm.Psp.Business.Country, App_Code",
			"Aplikační objekt": "FM.PSP.Country",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "1795385",
			"Název CZ": "Datová oblast",
			"Tabulka": "AMC_DataArea",
			"Primární klíč": "DataAreaID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DataArea, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "",
			"Modul": "Administrace"
		},
		{
			"ID": "1800458",
			"Název CZ": "Neutrální období indexace",
			"Tabulka": "FM_CM_NeutralIndexPeriod",
			"Primární klíč": "NeutralIndexPeriodID",
			"Třída": "Alstanet.Fm.Cm.Business.NeutralIndexPeriod, App_Code",
			"Aplikační objekt": "FM.CM.NeutralIndexPeriod",
			"Modul": "Smlouvy"
		},
		{
			"ID": "1811244",
			"Název CZ": "Role ořezávání dat",
			"Tabulka": "AMC_AccessRole",
			"Primární klíč": "AccessRoleID",
			"Třída": "Alstanet.Library.Web.AMC.Business.AccessRole, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.AccessRole",
			"Modul": "Administrace"
		},
		{
			"ID": "1828834",
			"Název CZ": "Podtyp nemovitosti",
			"Tabulka": "FM_PSP_BuildingSubType",
			"Primární klíč": "BuildingSubTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.BuildingSubType, App_Code",
			"Aplikační objekt": "FM.PSP.BuildingSubType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "1829343",
			"Název CZ": "Entita u odb. místa",
			"Tabulka": "FM_ENG_Place_Entity",
			"Primární klíč": "Place_EntityID",
			"Třída": "Alstanet.Fm.Eng.Business.Place_Entity, App_Code",
			"Aplikační objekt": "FM.ENG.Place_Entity",
			"Modul": "Energie"
		},
		{
			"ID": "1830735",
			"Název CZ": "Výkaz emisí",
			"Tabulka": "ACM_ESG_ACEEmissionReport",
			"Primární klíč": "ACEEmissionReportID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEmissionReport, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEmissionReport",
			"Modul": "ESG"
		},
		{
			"ID": "1858620",
			"Název CZ": "Pojistný program",
			"Tabulka": "FM_CF_Insurance",
			"Primární klíč": "InsuranceID",
			"Třída": "Alstanet.Fm.Cf.Business.Insurance, App_Code",
			"Aplikační objekt": "FM.CF.Insurance",
			"Modul": "Autopark"
		},
		{
			"ID": "1870229",
			"Název CZ": "Typ stavby",
			"Tabulka": "FM_CDS_BuildingType",
			"Primární klíč": "BuildingTypeID",
			"Třída": "Alstanet.Fm.Cds.Business.BuildingType, App_Code",
			"Aplikační objekt": "FM.CDS.BuildingType",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "1891605",
			"Název CZ": "Spalné teplo",
			"Tabulka": "FM_ENG_HeatOfComb",
			"Primární klíč": "HeatOfCombID",
			"Třída": "Alstanet.Fm.Eng.Business.HeatOfComb, App_Code",
			"Aplikační objekt": "FM.ENG.HeatOfComb",
			"Modul": "Energie"
		},
		{
			"ID": "1901804",
			"Název CZ": "Emisní limit",
			"Tabulka": "FM_CF_EmissionLimit",
			"Primární klíč": "EmissionLimitID",
			"Třída": "Alstanet.Fm.Cf.Business.EmissionLimit, App_Code",
			"Aplikační objekt": "FM.CF.EmissionLimit",
			"Modul": "Autopark"
		},
		{
			"ID": "1906716",
			"Název CZ": "Normalizační faktor",
			"Tabulka": "ACM_ESG_ACENormalizationFactor",
			"Primární klíč": "ACENormalizationFactorID",
			"Třída": "Alstanet.Acm.Esg.Business.ACENormalizationFactor, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACENormalizationFactor",
			"Modul": "ESG"
		},
		{
			"ID": "1925038",
			"Název CZ": "Dokument smlouvy",
			"Tabulka": "FM_CM_ACEContractDocument",
			"Primární klíč": "ACEContractDocumentID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEContractDocument, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEContractDocument",
			"Modul": "Smlouvy"
		},
		{
			"ID": "1935841",
			"Název CZ": "Income plan",
			"Tabulka": "FM_CM_IncomePlan",
			"Primární klíč": "IncomePlanID",
			"Třída": "Alstanet.Fm.Cm.Business.IncomePlan, App_Code",
			"Aplikační objekt": "FM.CM.IncomePlan",
			"Modul": "Smlouvy"
		},
		{
			"ID": "1961274",
			"Název CZ": "Šablona jízdy",
			"Tabulka": "FM_CF_TripTemplate",
			"Primární klíč": "TripTemplateID",
			"Třída": "Alstanet.Fm.Cf.Business.TripTemplate, App_Code",
			"Aplikační objekt": "FM.CF.TripTemplate",
			"Modul": "Autopark"
		},
		{
			"ID": "1987092",
			"Název CZ": "Tematická mapa",
			"Tabulka": "FM_CAD_ThematicMap",
			"Primární klíč": "ThematicMapID",
			"Třída": "Alstanet.Fm.Cad.Business.ThematicMap, App_Code",
			"Aplikační objekt": "FM.CAD.ThematicMap",
			"Modul": "Vizualizace"
		},
		{
			"ID": "1990395",
			"Název CZ": "Hořlavost",
			"Tabulka": "FM_TCH_Flammability",
			"Primární klíč": "FlammabilityID",
			"Třída": "Alstanet.Fm.Tch.Business.Flammability, App_Code",
			"Aplikační objekt": "FM.TCH.Flammability",
			"Modul": "Technologie"
		},
		{
			"ID": "2004615",
			"Název CZ": "Způsob využití katastrální stavby",
			"Tabulka": "FM_CDS_BuildingUsage",
			"Primární klíč": "BuildingUsageID",
			"Třída": "Alstanet.Fm.Cds.Business.BuildingUsage, App_Code",
			"Aplikační objekt": "FM.CDS.BuildingUsage",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "2039688",
			"Název CZ": "Pracovní doba",
			"Tabulka": "FM_FI_WorkTime",
			"Primární klíč": "WorkTimeID",
			"Třída": "Alstanet.Fm.Fi.Business.WorkTime, App_Code",
			"Aplikační objekt": "FM.FI.WorkTime",
			"Modul": "Ekonomika"
		},
		{
			"ID": "2043276",
			"Název CZ": "Break option - podána kým",
			"Tabulka": "FM_CM_BreakOptionGivenBy",
			"Primární klíč": "BreakOptionGivenByID",
			"Třída": "Alstanet.Fm.Cm.Business.BreakOptionGivenBy, App_Code",
			"Aplikační objekt": "FM.CM.BreakOptionGivenBy",
			"Modul": "Smlouvy"
		},
		{
			"ID": "2051744",
			"Název CZ": "Aplikační objekt",
			"Tabulka": "AMC_ApplObject",
			"Primární klíč": "ApplObjectID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ApplObject, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ApplObject",
			"Modul": "Administrace"
		},
		{
			"ID": "2052678",
			"Název CZ": "Typ inventury",
			"Tabulka": "FM_INV_InventoryType",
			"Primární klíč": "InventoryTypeID",
			"Třída": "Alstanet.Fm.Inv.Business.InventoryType, App_Code",
			"Aplikační objekt": "FM.INV.InventoryType",
			"Modul": "Inventury"
		},
		{
			"ID": "2056763",
			"Název CZ": "Městská část",
			"Tabulka": "FM_PSP_ACECityDistrict",
			"Primární klíč": "ACECityDistrictID",
			"Třída": "Alstanet.Fm.Psp.Business.ACECityDistrict, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACECityDistrict",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "2080277",
			"Název CZ": "Region",
			"Tabulka": "FM_PSP_Locality",
			"Primární klíč": "LocalityID",
			"Třída": "Alstanet.Fm.Psp.Business.Locality, App_Code",
			"Aplikační objekt": "FM.PSP.Locality",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "2097538",
			"Název CZ": "Uživatelská entita",
			"Tabulka": "AMC_CustEntity",
			"Primární klíč": "CustEntityID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustEntity, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustEntity",
			"Modul": "Administrace"
		},
		{
			"ID": "2106755",
			"Název CZ": "Katastrální Stavba",
			"Tabulka": "FM_CDS_Building",
			"Primární klíč": "BuildingID",
			"Třída": "Alstanet.Fm.Cds.Business.Building, App_Code",
			"Aplikační objekt": "FM.CDS.Building",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "2116866",
			"Název CZ": "OAuth aplikace",
			"Tabulka": "AMC_OAuthApplication",
			"Primární klíč": "OAuthApplicationID",
			"Třída": "Alstanet.Library.Web.AMC.Business.OAuthApplication, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.OAuthApplication",
			"Modul": "Administrace"
		},
		{
			"ID": "2122039",
			"Název CZ": "Metoda odpisování",
			"Tabulka": "FM_AST_DepreciationMethod",
			"Primární klíč": "DepreciationMethodID",
			"Třída": "Alstanet.Fm.Ast.Business.DepreciationMethod, App_Code",
			"Aplikační objekt": "FM.AST.DepreciationMethod",
			"Modul": "Majetek"
		},
		{
			"ID": "2128166",
			"Název CZ": "Přidělení klíče",
			"Tabulka": "FM_KCM_KeyAssign",
			"Primární klíč": "KeyAssignID",
			"Třída": "Alstanet.Fm.Kcm.Business.KeyAssign, App_Code",
			"Aplikační objekt": "FM.KCM.KeyAssign",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "2131420",
			"Název CZ": "Vývoz odpadu",
			"Tabulka": "FM_PSP_WasteExport",
			"Primární klíč": "WasteExportID",
			"Třída": "Alstanet.Fm.Psp.Business.WasteExport, App_Code",
			"Aplikační objekt": "FM.PSP.WasteExport",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "2175506",
			"Název CZ": "Specifikace pokrytí",
			"Tabulka": "FM_ENG_ACECoverageSpecification",
			"Primární klíč": "ACECoverageSpecificationID",
			"Třída": "Alstanet.Fm.Eng.Business.ACECoverageSpecification, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACECoverageSpecification",
			"Modul": "Energie"
		},
		{
			"ID": "2208424",
			"Název CZ": "Typ podílu",
			"Tabulka": "FM_IVO_FixedPaymentTariffType",
			"Primární klíč": "FixedPaymentTariffTypeID",
			"Třída": "Alstanet.Fm.Ivo.Business.FixedPaymentTariffType, App_Code",
			"Aplikační objekt": "FM.IVO.FixedPaymentTariffType",
			"Modul": "Faktury"
		},
		{
			"ID": "2213425",
			"Název CZ": "ESG scope",
			"Tabulka": "ACM_ESG_ACEESGScope",
			"Primární klíč": "ACEESGScopeID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEESGScope, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEESGScope",
			"Modul": "ESG"
		},
		{
			"ID": "2217330",
			"Název CZ": "Model vozidla",
			"Tabulka": "FM_CF_Model",
			"Primární klíč": "ModelID",
			"Třída": "Alstanet.Fm.Cf.Business.Model, App_Code",
			"Aplikační objekt": "",
			"Modul": "Autopark"
		},
		{
			"ID": "2258699",
			"Název CZ": "Cena PHM",
			"Tabulka": "FM_CF_FuelPrice",
			"Primární klíč": "FuelPriceID",
			"Třída": "Alstanet.Fm.Cf.Business.FuelPrice, App_Code",
			"Aplikační objekt": "FM.CF.FuelPrice",
			"Modul": "Autopark"
		},
		{
			"ID": "2283029",
			"Název CZ": "Kategorie zdroje energie",
			"Tabulka": "ACM_ESG_ACEEnergySourceCategory",
			"Primární klíč": "ACEEnergySourceCategoryID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEnergySourceCategory, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEnergySourceCategory",
			"Modul": "ESG"
		},
		{
			"ID": "2316324",
			"Název CZ": "Způsob vyřazení",
			"Tabulka": "FM_AST_DisposalMethod",
			"Primární klíč": "DisposalMethodID",
			"Třída": "Alstanet.Fm.Ast.Business.DisposalMethod, App_Code",
			"Aplikační objekt": "FM.AST.DisposalMethod",
			"Modul": "Majetek"
		},
		{
			"ID": "2316806",
			"Název CZ": "Break option",
			"Tabulka": "FM_CM_BreakOption",
			"Primární klíč": "BreakOptionID",
			"Třída": "Alstanet.Fm.Cm.Business.BreakOption, App_Code",
			"Aplikační objekt": "FM.CM.BreakOption",
			"Modul": "Smlouvy"
		},
		{
			"ID": "2325469",
			"Název CZ": "Rozpad nad",
			"Tabulka": "FM_IVO_DistributionFrom",
			"Primární klíč": "DistributionFromID",
			"Třída": "Alstanet.Fm.Ivo.Business.DistributionFrom, App_Code",
			"Aplikační objekt": "FM.IVO.DistributionFrom",
			"Modul": "Faktury"
		},
		{
			"ID": "2369560",
			"Název CZ": "Využití parcely",
			"Tabulka": "FM_CDS_ParcelUsage",
			"Primární klíč": "ParcelUsageID",
			"Třída": "Alstanet.Fm.Cds.Business.ParcelUsage, App_Code",
			"Aplikační objekt": "FM.CDS.ParcelUsage",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "2425773",
			"Název CZ": "Typ záruky",
			"Tabulka": "FM_CM_ContractGuaranteeType",
			"Primární klíč": "ContractGuaranteeTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractGuaranteeType, App_Code",
			"Aplikační objekt": "FM.CM.ContractGuaranteeType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "2426614",
			"Název CZ": "Metoda značení",
			"Tabulka": "FM_AST_MarkingMethod",
			"Primární klíč": "MarkingMethodID",
			"Třída": "Alstanet.Fm.Ast.Business.MarkingMethod, App_Code",
			"Aplikační objekt": "FM.AST.MarkingMethod",
			"Modul": "Majetek"
		},
		{
			"ID": "2429349",
			"Název CZ": "Směr",
			"Tabulka": "AMC_ScheduleFreqRelIntDirect",
			"Primární klíč": "ScheduleFreqRelIntDirectionID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ScheduleFreqRelIntDirect, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ScheduleFreqRelIntDirection",
			"Modul": "Administrace"
		},
		{
			"ID": "2466994",
			"Název CZ": "Odkaz",
			"Tabulka": "FM_CMS_ArticleHyperlink",
			"Primární klíč": "ArticleHyperlinkID",
			"Třída": "Alstanet.Fm.Cms.Business.ArticleHyperlink, App_Code",
			"Aplikační objekt": "FM.CMS.ArticleHyperlink",
			"Modul": "Portál"
		},
		{
			"ID": "2513691",
			"Název CZ": "Typ položky",
			"Tabulka": "FM_MNT_ItemType",
			"Primární klíč": "ItemTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.ItemType, App_Code",
			"Aplikační objekt": "FM.MNT.ItemType",
			"Modul": "Údržba"
		},
		{
			"ID": "2516859",
			"Název CZ": "Typ atributu",
			"Tabulka": "AMC_PropertyType",
			"Primární klíč": "PropertyTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.PropertyType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.PropertyType",
			"Modul": "Administrace"
		},
		{
			"ID": "2531972",
			"Název CZ": "Locker",
			"Tabulka": "ACM_LOC_ACELockers",
			"Primární klíč": "ACELockersID",
			"Třída": "Alstanet.Acm.Loc.Business.ACELockers, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.LOC.ACELockers",
			"Modul": "Lockers"
		},
		{
			"ID": "2544499",
			"Název CZ": "Verze",
			"Tabulka": "FM_DMS_Version",
			"Primární klíč": "VersionID",
			"Třída": "Alstanet.Fm.Dms.Business.Version, App_Code",
			"Aplikační objekt": "FM.DMS.Version",
			"Modul": "DMS"
		},
		{
			"ID": "2545424",
			"Název CZ": "Typ dokumentu",
			"Tabulka": "FM_DMS_DocumentType",
			"Primární klíč": "DocumentTypeID",
			"Třída": "Alstanet.Fm.Dms.Business.DocumentType, App_Code",
			"Aplikační objekt": "FM.DMS.DocumentType",
			"Modul": "DMS"
		},
		{
			"ID": "2552971",
			"Název CZ": "Přesměrování emailu",
			"Tabulka": "AMC_EmailRedirect",
			"Primární klíč": "EmailRedirectID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailRedirect, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailRedirect",
			"Modul": "Administrace"
		},
		{
			"ID": "2553492",
			"Název CZ": "Přesnost dat",
			"Tabulka": "ACM_ESG_ACEDataAccuracy",
			"Primární klíč": "ACEDataAccuracyID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEDataAccuracy, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEDataAccuracy",
			"Modul": "ESG"
		},
		{
			"ID": "2636761",
			"Název CZ": "Třída majetku",
			"Tabulka": "FM_AST_AssetGroup",
			"Primární klíč": "AssetGroupID",
			"Třída": "Alstanet.Fm.Ast.Business.AssetGroup, App_Code",
			"Aplikační objekt": "FM.AST.AssetGroup",
			"Modul": "Majetek"
		},
		{
			"ID": "2644530",
			"Název CZ": "Heslo",
			"Tabulka": "AMC_Password",
			"Primární klíč": "PasswordID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Password, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Password",
			"Modul": "Administrace"
		},
		{
			"ID": "2655838",
			"Název CZ": "Den opakování",
			"Tabulka": "AMC_ScheduleFreqWeekday",
			"Primární klíč": "ScheduleFreqWeekdayID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ScheduleFreqWeekday, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ScheduleFreqWeekday",
			"Modul": "Administrace"
		},
		{
			"ID": "2660319",
			"Název CZ": "Třída",
			"Tabulka": "AMC_EventLogClass",
			"Primární klíč": "EventLogClassID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogClass, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogClass",
			"Modul": "Administrace"
		},
		{
			"ID": "2661442",
			"Název CZ": "Priorita nemovitosti",
			"Tabulka": "FM_PSP_BuildingPriority",
			"Primární klíč": "BuildingPriorityID",
			"Třída": "Alstanet.Fm.Psp.Business.BuildingPriority, App_Code",
			"Aplikační objekt": "FM.PSP.BuildingPriority",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "2685832",
			"Název CZ": "Třída měření",
			"Tabulka": "FM_ENG_ACEMeasureClass",
			"Primární klíč": "ACEMeasureClassID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEMeasureClass, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEMeasureClass",
			"Modul": "Energie"
		},
		{
			"ID": "2688760",
			"Název CZ": "Nastavení KPI",
			"Tabulka": "FM_WF_KpiTimeSetup",
			"Primární klíč": "KpiTimeSetupID",
			"Třída": "Alstanet.Fm.Wf.Business.KpiTimeSetup, App_Code",
			"Aplikační objekt": "FM.WF.KpiTimeSetup",
			"Modul": "Workflow"
		},
		{
			"ID": "2696785",
			"Název CZ": "Typ spojení",
			"Tabulka": "AMC_ConnectionType",
			"Primární klíč": "ConnectionTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ConnectionType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ConnectionType",
			"Modul": "Administrace"
		},
		{
			"ID": "2728095",
			"Název CZ": "Pozice osoby",
			"Tabulka": "FM_HR_EmployeePosition",
			"Primární klíč": "EmployeePositionID",
			"Třída": "Alstanet.Fm.Hr.Business.EmployeePosition, App_Code",
			"Aplikační objekt": "FM.HR.EmployeePosition",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "2776919",
			"Název CZ": "ESRS report",
			"Tabulka": "ACM_ESG_ACEESRSReport",
			"Primární klíč": "ACEESRSReportID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEESRSReport, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEESRSReport",
			"Modul": "ESG"
		},
		{
			"ID": "2793057",
			"Název CZ": "Typ incidentu",
			"Tabulka": "FM_CF_IncidentReportType",
			"Primární klíč": "IncidentReportTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.IncidentReportType, App_Code",
			"Aplikační objekt": "FM.CF.IncidentReportType",
			"Modul": "Autopark"
		},
		{
			"ID": "2793366",
			"Název CZ": "KPI přiznané - pravidlo",
			"Tabulka": "FM_MNT_KpiConfirmed",
			"Primární klíč": "KpiConfirmedID",
			"Třída": "Alstanet.Fm.Mnt.Business.KpiConfirmed, App_Code",
			"Aplikační objekt": "FM.MNT.KpiConfirmed",
			"Modul": "Údržba"
		},
		{
			"ID": "2805155",
			"Název CZ": "Tenancy schedule",
			"Tabulka": "FM_CM_TenancySchedule",
			"Primární klíč": "TenancyScheduleID",
			"Třída": "Alstanet.Fm.Cm.Business.TenancySchedule, App_Code",
			"Aplikační objekt": "FM.CM.TenancySchedule",
			"Modul": "Smlouvy"
		},
		{
			"ID": "2828697",
			"Název CZ": "Udělení přístupu k účtu",
			"Tabulka": "AMC_UserAccessGrant",
			"Primární klíč": "UserAccessGrantID",
			"Třída": "Alstanet.Library.Web.AMC.Business.UserAccessGrant, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.UserAccessGrant",
			"Modul": "Administrace"
		},
		{
			"ID": "2834529",
			"Název CZ": "Strop",
			"Tabulka": "FM_PSP_CeilingPart",
			"Primární klíč": "CeilingPartID",
			"Třída": "Alstanet.Fm.Psp.Business.CeilingPart, App_Code",
			"Aplikační objekt": "FM.PSP.CeilingPart",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "2839583",
			"Název CZ": "Přidělení karty",
			"Tabulka": "FM_KCM_CardAssign",
			"Primární klíč": "CardAssignID",
			"Třída": "Alstanet.Fm.Kcm.Business.CardAssign, App_Code",
			"Aplikační objekt": "FM.KCM.CardAssign",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "2841917",
			"Název CZ": "Historie podílu",
			"Tabulka": "FM_IVO_DistributionKeyShareLog",
			"Primární klíč": "DistributionKeyShareLogID",
			"Třída": "Alstanet.Fm.Ivo.Business.DistributionKeyShareLog, App_Code",
			"Aplikační objekt": "FM.IVO.DistributionKeyShareLog",
			"Modul": "Faktury"
		},
		{
			"ID": "2845315",
			"Název CZ": "Typ pojištění",
			"Tabulka": "FM_CF_InsuranceType",
			"Primární klíč": "InsuranceTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.InsuranceType, App_Code",
			"Aplikační objekt": "FM.CF.InsuranceType",
			"Modul": "Autopark"
		},
		{
			"ID": "2845914",
			"Název CZ": "Mapování entity na typ vrstvy",
			"Tabulka": "FM_CAD_LayerTypeEntityMapping",
			"Primární klíč": "LayerTypeEntityMappingID",
			"Třída": "Alstanet.Fm.Cad.Business.LayerTypeEntityMapping, App_Code",
			"Aplikační objekt": "FM.CAD.LayerTypeEntityMapping",
			"Modul": "Vizualizace"
		},
		{
			"ID": "2858375",
			"Název CZ": "Stav vozidla",
			"Tabulka": "FM_CF_CarStatus",
			"Primární klíč": "CarStatusID",
			"Třída": "Alstanet.Fm.Cf.Business.CarStatus, App_Code",
			"Aplikační objekt": "FM.CF.CarStatus",
			"Modul": "Autopark"
		},
		{
			"ID": "2901334",
			"Název CZ": "Parkovací místo",
			"Tabulka": "FM_CF_ParkPlace",
			"Primární klíč": "ParkPlaceID",
			"Třída": "Alstanet.Fm.Cf.Business.ParkPlace, App_Code",
			"Aplikační objekt": "FM.CF.ParkPlace",
			"Modul": "Autopark"
		},
		{
			"ID": "2928562",
			"Název CZ": "Orientace místnosti",
			"Tabulka": "FM_PSP_RoomOrientation",
			"Primární klíč": "RoomOrientationID",
			"Třída": "Alstanet.Fm.Psp.Business.RoomOrientation, App_Code",
			"Aplikační objekt": "FM.PSP.RoomOrientation",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "2952427",
			"Název CZ": "Položka checklistu",
			"Tabulka": "FM_MNT_ChecklistItem",
			"Primární klíč": "ChecklistItemID",
			"Třída": "Alstanet.Fm.Mnt.Business.ChecklistItem, App_Code",
			"Aplikační objekt": "FM.MNT.ChecklistItem",
			"Modul": "Údržba"
		},
		{
			"ID": "2974067",
			"Název CZ": "Povolený odpad",
			"Tabulka": "ACM_WST_ACEGrantedWaste",
			"Primární klíč": "ACEGrantedWasteID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEGrantedWaste, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEGrantedWaste",
			"Modul": "Odpady"
		},
		{
			"ID": "2981579",
			"Název CZ": "Druh kapacity",
			"Tabulka": "FM_ENG_CapacityType",
			"Primární klíč": "CapacityTypeID",
			"Třída": "Alstanet.Fm.Eng.Business.CapacityType, App_Code",
			"Aplikační objekt": "FM.ENG.CapacityType",
			"Modul": "Energie"
		},
		{
			"ID": "2987030",
			"Název CZ": "Typ jízdy",
			"Tabulka": "FM_CF_DrivebookItemType",
			"Primární klíč": "DrivebookItemTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.DrivebookItemType, App_Code",
			"Aplikační objekt": "FM.CF.DrivebookItemType",
			"Modul": "Autopark"
		},
		{
			"ID": "2989256",
			"Název CZ": "Typ parametru",
			"Tabulka": "AMC_CustParamType",
			"Primární klíč": "CustParamTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustParamType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustParamType",
			"Modul": "Administrace"
		},
		{
			"ID": "3013007",
			"Název CZ": "Druh parcely",
			"Tabulka": "FM_CDS_ParcelKind",
			"Primární klíč": "ParcelKindID",
			"Třída": "Alstanet.Fm.Cds.Business.ParcelKind, App_Code",
			"Aplikační objekt": "FM.CDS.ParcelKind",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "3014764",
			"Název CZ": "Kategorie emisí",
			"Tabulka": "ACM_ESG_ACEEmissionCategory",
			"Primární klíč": "ACEEmissionCategoryID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEmissionCategory, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEmissionCategory",
			"Modul": "ESG"
		},
		{
			"ID": "3026353",
			"Název CZ": "Typ 3 majetku",
			"Tabulka": "FM_AST_Type3",
			"Primární klíč": "Type3ID",
			"Třída": "Alstanet.Fm.Ast.Business.Type3, App_Code",
			"Aplikační objekt": "FM.AST.Type3",
			"Modul": "Majetek"
		},
		{
			"ID": "3028791",
			"Název CZ": "Skleníkový plyn",
			"Tabulka": "ACM_ESG_ACEGreenHouseGas",
			"Primární klíč": "ACEGreenHouseGasID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEGreenHouseGas, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEGreenHouseGas",
			"Modul": "ESG"
		},
		{
			"ID": "3035866",
			"Název CZ": "Položka tematické mapy",
			"Tabulka": "FM_CAD_ThematicMapItem",
			"Primární klíč": "ThematicMapItemID",
			"Třída": "Alstanet.Fm.Cad.Business.ThematicMapItem, App_Code",
			"Aplikační objekt": "FM.CAD.ThematicMapItem",
			"Modul": "Vizualizace"
		},
		{
			"ID": "3068864",
			"Název CZ": "Platební morálka",
			"Tabulka": "FM_CM_ACEPaymentEthics",
			"Primární klíč": "ACEPaymentEthicsID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEPaymentEthics, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEPaymentEthics",
			"Modul": "Smlouvy"
		},
		{
			"ID": "3109066",
			"Název CZ": "Zdroj",
			"Tabulka": "FM_FI_Source",
			"Primární klíč": "SourceID",
			"Třída": "Alstanet.Fm.Fi.Business.Source, App_Code",
			"Aplikační objekt": "FM.FI.Source",
			"Modul": "Ekonomika"
		},
		{
			"ID": "3128654",
			"Název CZ": "Kategorie vozidla",
			"Tabulka": "FM_CF_CarCategory",
			"Primární klíč": "CarCategoryID",
			"Třída": "Alstanet.Fm.Cf.Business.CarCategory, App_Code",
			"Aplikační objekt": "FM.CF.CarCategory",
			"Modul": "Autopark"
		},
		{
			"ID": "3136961",
			"Název CZ": "Účetní stav majetku",
			"Tabulka": "FM_AST_AssetStatus",
			"Primární klíč": "AssetStatusID",
			"Třída": "Alstanet.Fm.Ast.Business.AssetStatus, App_Code",
			"Aplikační objekt": "FM.AST.AssetStatus",
			"Modul": "Majetek"
		},
		{
			"ID": "3150640",
			"Název CZ": "Kvalifikace zaměstnance",
			"Tabulka": "FM_HR_Employee_Qualification",
			"Primární klíč": "Employee_QualificationID",
			"Třída": "Alstanet.Fm.Hr.Business.Employee_Qualification, App_Code",
			"Aplikační objekt": "FM.HR.Employee_Qualification",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "3168580",
			"Název CZ": "Modul",
			"Tabulka": "FM_CMS_ArticleModule",
			"Primární klíč": "ArticleModuleID",
			"Třída": "Alstanet.Fm.Cms.Business.ArticleModule, App_Code",
			"Aplikační objekt": "FM.CMS.ArticleModule",
			"Modul": "Portál"
		},
		{
			"ID": "3178105",
			"Název CZ": "Typ tematické mapy",
			"Tabulka": "FM_CAD_ThematicMapType",
			"Primární klíč": "ThematicMapTypeID",
			"Třída": "Alstanet.Fm.Cad.Business.ThematicMapType, App_Code",
			"Aplikační objekt": "FM.CAD.ThematicMapType",
			"Modul": "Vizualizace"
		},
		{
			"ID": "3198579",
			"Název CZ": "Typ revizní zprávy",
			"Tabulka": "FM_MNT_RevisionType",
			"Primární klíč": "RevisionTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.RevisionType, App_Code",
			"Aplikační objekt": "FM.MNT.RevisionType",
			"Modul": "Údržba"
		},
		{
			"ID": "3205074",
			"Název CZ": "Objednávka",
			"Tabulka": "FM_ORD_Order",
			"Primární klíč": "OrderID",
			"Třída": "Alstanet.Fm.Ord.Business.Order, App_Code",
			"Aplikační objekt": "FM.ORD.Order",
			"Modul": "Objednávky"
		},
		{
			"ID": "3217620",
			"Název CZ": "Silniční daně",
			"Tabulka": "FM_CF_RoadTax",
			"Primární klíč": "RoadTaxID",
			"Třída": "Alstanet.Fm.Cf.Business.RoadTax, App_Code",
			"Aplikační objekt": "FM.CF.RoadTax",
			"Modul": "Autopark"
		},
		{
			"ID": "3220300",
			"Název CZ": "Typ uživatele",
			"Tabulka": "AMC_UserType",
			"Primární klíč": "UserTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.UserType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.UserType",
			"Modul": "Administrace"
		},
		{
			"ID": "3264069",
			"Název CZ": "Místnost na jednotce",
			"Tabulka": "FM_PSP_UnitGroupRoom",
			"Primární klíč": "UnitGroupRoomID",
			"Třída": "Alstanet.Fm.Psp.Business.UnitGroupRoom, App_Code",
			"Aplikační objekt": "FM.PSP.UnitGroupRoom",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "3266995",
			"Název CZ": "Skupina bezpečnosti",
			"Tabulka": "FM_TCH_SecurityGroup",
			"Primární klíč": "SecurityGroupID",
			"Třída": "Alstanet.Fm.Tch.Business.SecurityGroup, App_Code",
			"Aplikační objekt": "FM.TCH.SecurityGroup",
			"Modul": "Technologie"
		},
		{
			"ID": "3277452",
			"Název CZ": "Jazyk",
			"Tabulka": "AMC_Lang",
			"Primární klíč": "LangID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Lang, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Lang",
			"Modul": "Administrace"
		},
		{
			"ID": "3304775",
			"Název CZ": "Activity log",
			"Tabulka": "AMC_ActivityLog",
			"Primární klíč": "ActivityLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ActivityLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ActivityLog",
			"Modul": "Administrace"
		},
		{
			"ID": "3318217",
			"Název CZ": "Převod jednotek",
			"Tabulka": "FM_FI_MeasureUnitTransfer",
			"Primární klíč": "MeasureUnitTransferID",
			"Třída": "Alstanet.Fm.Fi.Business.MeasureUnitTransfer, App_Code",
			"Aplikační objekt": "FM.FI.MeasureUnitTransfer",
			"Modul": "Ekonomika"
		},
		{
			"ID": "3343842",
			"Název CZ": "Subjekt u entity",
			"Tabulka": "FM_HR_Subject_Entity",
			"Primární klíč": "Subject_EntityID",
			"Třída": "Alstanet.Fm.Hr.Business.Subject_Entity, App_Code",
			"Aplikační objekt": "FM.HR.Subject_Entity",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "3351940",
			"Název CZ": "Dálniční známka",
			"Tabulka": "FM_CF_TollSticker",
			"Primární klíč": "TollStickerID",
			"Třída": "Alstanet.Fm.Cf.Business.TollSticker, App_Code",
			"Aplikační objekt": "FM.CF.TollSticker",
			"Modul": "Autopark"
		},
		{
			"ID": "3363159",
			"Název CZ": "Závažnost vady",
			"Tabulka": "FM_MNT_FaultPriority",
			"Primární klíč": "FaultPriorityID",
			"Třída": "Alstanet.Fm.Mnt.Business.FaultPriority, App_Code",
			"Aplikační objekt": "FM.MNT.FaultPriority",
			"Modul": "Údržba"
		},
		{
			"ID": "3381849",
			"Název CZ": "Skupina zboží",
			"Tabulka": "FM_CF_GoodsGroup",
			"Primární klíč": "GoodsGroupID",
			"Třída": "Alstanet.Fm.Cf.Business.GoodsGroup, App_Code",
			"Aplikační objekt": "FM.CF.GoodsGroup",
			"Modul": "Autopark"
		},
		{
			"ID": "3386515",
			"Název CZ": "Modul",
			"Tabulka": "AMC_EntityTypeGroup",
			"Primární klíč": "EntityTypeGroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EntityTypeGroup, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EntityTypeGroup",
			"Modul": "Administrace"
		},
		{
			"ID": "3402560",
			"Název CZ": "Druh odpadu",
			"Tabulka": "FM_PSP_WasteType",
			"Primární klíč": "WasteTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.WasteType, App_Code",
			"Aplikační objekt": "FM.PSP.WasteType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "3406011",
			"Název CZ": "Skupina",
			"Tabulka": "AMC_Group",
			"Primární klíč": "GroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Group, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Group",
			"Modul": "Administrace"
		},
		{
			"ID": "3409577",
			"Název CZ": "Typ jednotky",
			"Tabulka": "FM_PSP_UnitGroupType",
			"Primární klíč": "UnitGroupTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.UnitGroupType, App_Code",
			"Aplikační objekt": "FM.PSP.UnitGroupType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "3426880",
			"Název CZ": "Emisní faktory",
			"Tabulka": "ACM_WST_ACEWasteEmissionFactor",
			"Primární klíč": "ACEWasteEmissionFactorID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteEmissionFactor, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteEmissionFactor",
			"Modul": "Odpady"
		},
		{
			"ID": "3427265",
			"Název CZ": "Typ 2 majetku",
			"Tabulka": "FM_AST_Type2",
			"Primární klíč": "Type2ID",
			"Třída": "Alstanet.Fm.Ast.Business.Type2, App_Code",
			"Aplikační objekt": "FM.AST.Type2",
			"Modul": "Majetek"
		},
		{
			"ID": "3436004",
			"Název CZ": "Návrh na vyřazení",
			"Tabulka": "FM_AST_Proposal",
			"Primární klíč": "ProposalID",
			"Třída": "Alstanet.Fm.Ast.Business.Proposal, App_Code",
			"Aplikační objekt": "FM.AST.Proposal",
			"Modul": "Majetek"
		},
		{
			"ID": "3454314",
			"Název CZ": "Log události",
			"Tabulka": "AMC_EventLog",
			"Primární klíč": "EventLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLog",
			"Modul": "Administrace"
		},
		{
			"ID": "3456714",
			"Název CZ": "Typ nabídky",
			"Tabulka": "FM_ORD_OfferType",
			"Primární klíč": "OfferTypeID",
			"Třída": "Alstanet.Fm.Ord.Business.OfferType, App_Code",
			"Aplikační objekt": "FM.ORD.OfferType",
			"Modul": "Objednávky"
		},
		{
			"ID": "3476473",
			"Název CZ": "Vazba technologií",
			"Tabulka": "FM_TCH_Dependence",
			"Primární klíč": "DependenceID",
			"Třída": "Alstanet.Fm.Tch.Business.Dependence, App_Code",
			"Aplikační objekt": "FM.TCH.Dependence",
			"Modul": "Technologie"
		},
		{
			"ID": "3482328",
			"Název CZ": "Ceník hlavního jističe",
			"Tabulka": "FM_ENG_MainCircBreakPrice",
			"Primární klíč": "MainCircBreakPriceID",
			"Třída": "Alstanet.Fm.Eng.Business.MainCircBreakPrice, App_Code",
			"Aplikační objekt": "FM.ENG.MainCircBreakPrice",
			"Modul": "Energie"
		},
		{
			"ID": "3514431",
			"Název CZ": "Entita na článku",
			"Tabulka": "FM_CMS_Article_Entity",
			"Primární klíč": "Article_EntityID",
			"Třída": "Alstanet.Fm.Cms.Business.Article_Entity, App_Code",
			"Aplikační objekt": "FM.CMS.Article_Entity",
			"Modul": "Portál"
		},
		{
			"ID": "3525109",
			"Název CZ": "Definice práv",
			"Tabulka": "AMC_ACL",
			"Primární klíč": "ACLID",
			"Třída": "Alstanet.Library.Web.AMC.Business.PermDefinition, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ACL",
			"Modul": "Administrace"
		},
		{
			"ID": "3531345",
			"Název CZ": "Položka NK",
			"Tabulka": "FM_MNT_ShoppingCartItem",
			"Primární klíč": "ShoppingCartItemID",
			"Třída": "Alstanet.Fm.Mnt.Business.ShoppingCartItem, App_Code",
			"Aplikační objekt": "FM.MNT.ShoppingCartItem",
			"Modul": "Údržba"
		},
		{
			"ID": "3534028",
			"Název CZ": "Historie plochy",
			"Tabulka": "FM_PSP_ACERoomAreaLog",
			"Primární klíč": "ACERoomAreaLogID",
			"Třída": "Alstanet.Fm.Psp.Business.ACERoomAreaLog, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACERoomAreaLog",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "3553959",
			"Název CZ": "Položka slovníku",
			"Tabulka": "AMC_LangDictionaryEntry",
			"Primární klíč": "LangDictionaryEntryID",
			"Třída": "Alstanet.Library.Web.AMC.Business.LangDictionaryEntry, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.LangDictionaryEntry",
			"Modul": "Administrace"
		},
		{
			"ID": "3557742",
			"Název CZ": "Poskytovatel GPS",
			"Tabulka": "FM_CF_GPSProvider",
			"Primární klíč": "GPSProviderID",
			"Třída": "Alstanet.Fm.Cf.Business.GPSProvider, App_Code",
			"Aplikační objekt": "FM.CF.GPSProvider",
			"Modul": "Autopark"
		},
		{
			"ID": "3573144",
			"Název CZ": "Typ majetku",
			"Tabulka": "FM_AST_Type1",
			"Primární klíč": "Type1ID",
			"Třída": "Alstanet.Fm.Ast.Business.Type1, App_Code",
			"Aplikační objekt": "FM.AST.Type1",
			"Modul": "Majetek"
		},
		{
			"ID": "3644587",
			"Název CZ": "Skupina atributů",
			"Tabulka": "AMC_CustPropertyGroup",
			"Primární klíč": "CustPropertyGroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustPropertyGroup, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustPropertyGroup",
			"Modul": "Administrace"
		},
		{
			"ID": "3656899",
			"Název CZ": "Sazba",
			"Tabulka": "FM_ENG_Rate",
			"Primární klíč": "RateID",
			"Třída": "Alstanet.Fm.Eng.Business.Rate, App_Code",
			"Aplikační objekt": "FM.ENG.Rate",
			"Modul": "Energie"
		},
		{
			"ID": "3670787",
			"Název CZ": "Výběr kurzu",
			"Tabulka": "FM_CM_ExchangeRateType",
			"Primární klíč": "ExchangeRateTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.ExchangeRateType, App_Code",
			"Aplikační objekt": "FM.CM.ExchangeRateType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "3677046",
			"Název CZ": "Kategorie materiálu",
			"Tabulka": "FM_FI_MaterialCategory",
			"Primární klíč": "MaterialCategoryID",
			"Třída": "Alstanet.Fm.Fi.Business.MaterialCategory, App_Code",
			"Aplikační objekt": "FM.FI.MaterialCategory",
			"Modul": "Ekonomika"
		},
		{
			"ID": "3696654",
			"Název CZ": "Plánovaná úloha",
			"Tabulka": "AMC_Schedule",
			"Primární klíč": "ScheduleID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Schedule, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Schedule",
			"Modul": "Administrace"
		},
		{
			"ID": "3697627",
			"Název CZ": "Typ grafického objektu",
			"Tabulka": "FM_CAD_GraphicsObjectType",
			"Primární klíč": "GraphicsObjectTypeID",
			"Třída": "Alstanet.Fm.Cad.Business.GraphicsObjectType, App_Code",
			"Aplikační objekt": "FM.CAD.GraphicsObjectType",
			"Modul": "Vizualizace"
		},
		{
			"ID": "3714753",
			"Název CZ": "Omezení dat",
			"Tabulka": "AMC_EntityAccessRestriction",
			"Primární klíč": "EntityAccessRestrictionID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EntityAccessRestriction, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EntityAccessRestriction",
			"Modul": "Administrace"
		},
		{
			"ID": "3717431",
			"Název CZ": "Typ vrstvy",
			"Tabulka": "FM_CAD_LayerType",
			"Primární klíč": "LayerTypeID",
			"Třída": "Alstanet.Fm.Cad.Business.LayerType, App_Code",
			"Aplikační objekt": "FM.CAD.LayerType",
			"Modul": "Vizualizace"
		},
		{
			"ID": "3725699",
			"Název CZ": "Typ logu události",
			"Tabulka": "AMC_EventLogType",
			"Primární klíč": "EventLogTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogType",
			"Modul": "Administrace"
		},
		{
			"ID": "3788995",
			"Název CZ": "Článek nápovědy",
			"Tabulka": "AMC_HelpArticle",
			"Primární klíč": "HelpArticleID",
			"Třída": "Alstanet.Library.Web.AMC.Business.HelpArticle, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Help.HelpArticle",
			"Modul": "Administrace"
		},
		{
			"ID": "3825926",
			"Název CZ": "Disk",
			"Tabulka": "FM_CF_TireDisk",
			"Primární klíč": "TireDiskID",
			"Třída": "Alstanet.Fm.Cf.Business.TireDisk, App_Code",
			"Aplikační objekt": "FM.CF.TireDisk",
			"Modul": "Autopark"
		},
		{
			"ID": "3831763",
			"Název CZ": "Perioda aktualizace",
			"Tabulka": "FM_IVO_UpdatePeriod",
			"Primární klíč": "UpdatePeriodID",
			"Třída": "Alstanet.Fm.Ivo.Business.UpdatePeriod, App_Code",
			"Aplikační objekt": "FM.IVO.UpdatePeriod",
			"Modul": "Faktury"
		},
		{
			"ID": "3837733",
			"Název CZ": "Parkovací karta",
			"Tabulka": "FM_CF_ParkCard",
			"Primární klíč": "ParkCardID",
			"Třída": "Alstanet.Fm.Cf.Business.ParkCard, App_Code",
			"Aplikační objekt": "FM.CF.ParkCard",
			"Modul": "Autopark"
		},
		{
			"ID": "3860417",
			"Název CZ": "Podkategorie materiálu",
			"Tabulka": "FM_FI_MaterialSubCategory",
			"Primární klíč": "MaterialSubCategoryID",
			"Třída": "Alstanet.Fm.Fi.Business.MaterialSubCategory, App_Code",
			"Aplikační objekt": "FM.FI.MaterialSubCategory",
			"Modul": "Ekonomika"
		},
		{
			"ID": "3867546",
			"Název CZ": "Typ logu emailu",
			"Tabulka": "AMC_EmailLogParentType",
			"Primární klíč": "EmailLogParentTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailLogParentType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailLogParentType",
			"Modul": "Administrace"
		},
		{
			"ID": "3875017",
			"Název CZ": "Druh nákladu",
			"Tabulka": "FM_FI_CostKind",
			"Primární klíč": "CostKindID",
			"Třída": "Alstanet.Fm.Fi.Business.CostKind, App_Code",
			"Aplikační objekt": "FM.FI.CostKind",
			"Modul": "Ekonomika"
		},
		{
			"ID": "3888624",
			"Název CZ": "Rezervace vozidla",
			"Tabulka": "FM_CF_CarReservation",
			"Primární klíč": "CarReservationID",
			"Třída": "Alstanet.Fm.Cf.Business.CarReservation, App_Code",
			"Aplikační objekt": "FM.CF.CarReservation",
			"Modul": "Autopark"
		},
		{
			"ID": "3898615",
			"Název CZ": "Parcela",
			"Tabulka": "FM_CDS_Parcel",
			"Primární klíč": "ParcelID",
			"Třída": "Alstanet.Fm.Cds.Business.Parcel, App_Code",
			"Aplikační objekt": "FM.CDS.Parcel",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "3903597",
			"Název CZ": "Typ adresáta",
			"Tabulka": "FM_IVO_RecipientType",
			"Primární klíč": "RecipientTypeID",
			"Třída": "Alstanet.Fm.Ivo.Business.RecipientType, App_Code",
			"Aplikační objekt": "FM.IVO.RecipientType",
			"Modul": "Faktury"
		},
		{
			"ID": "3904111",
			"Název CZ": "Analytika",
			"Tabulka": "FM_MNT_Analytic",
			"Primární klíč": "AnalyticID",
			"Třída": "Alstanet.Fm.Mnt.Business.Analytic, App_Code",
			"Aplikační objekt": "FM.MNT.Analytic",
			"Modul": "Údržba"
		},
		{
			"ID": "3994672",
			"Název CZ": "Token",
			"Tabulka": "AMC_Token",
			"Primární klíč": "TokenID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Token, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Token",
			"Modul": "Administrace"
		},
		{
			"ID": "4015843",
			"Název CZ": "Typ jednotek",
			"Tabulka": "FM_FI_MeasureUnitType",
			"Primární klíč": "MeasureUnitTypeID",
			"Třída": "Alstanet.Fm.Fi.Business.MeasureUnitType, App_Code",
			"Aplikační objekt": "FM.FI.MeasureUnitType",
			"Modul": "Ekonomika"
		},
		{
			"ID": "4016872",
			"Název CZ": "Přístup k entitě",
			"Tabulka": "AMC_AccessEntity",
			"Primární klíč": "AccessEntityID",
			"Třída": "Alstanet.Library.Web.AMC.Business.AccessEntity, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.AccessEntity",
			"Modul": "Administrace"
		},
		{
			"ID": "4018446",
			"Název CZ": "Zdroj energie",
			"Tabulka": "ACM_ESG_ACEEnergySource",
			"Primární klíč": "ACEEnergySourceID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEnergySource, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEnergySource",
			"Modul": "ESG"
		},
		{
			"ID": "4046335",
			"Název CZ": "Šablona schvalovatele",
			"Tabulka": "FM_WF_ApproverTemplate",
			"Primární klíč": "ApproverTemplateID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproverTemplate, App_Code",
			"Aplikační objekt": "FM.WF.ApproverTemplate",
			"Modul": "Workflow"
		},
		{
			"ID": "4052297",
			"Název CZ": "Smluvní úkon",
			"Tabulka": "FM_CM_ACEContractAct",
			"Primární klíč": "ACEContractActID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEContractAct, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEContractAct",
			"Modul": "Smlouvy"
		},
		{
			"ID": "4174156",
			"Název CZ": "Log emailu",
			"Tabulka": "AMC_EmailLog",
			"Primární klíč": "EmailLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailLog",
			"Modul": "Administrace"
		},
		{
			"ID": "4216143",
			"Název CZ": "Typ energetického mixu",
			"Tabulka": "ACM_ESG_ACEEnergyMixType",
			"Primární klíč": "ACEEnergyMixTypeID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEnergyMixType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEnergyMixType",
			"Modul": "ESG"
		},
		{
			"ID": "4257237",
			"Název CZ": "Událost",
			"Tabulka": "AMC_EventLogEvent",
			"Primární klíč": "EventLogEventID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogEvent, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogEvent",
			"Modul": "Administrace"
		},
		{
			"ID": "4258781",
			"Název CZ": "Podmíněné použití",
			"Tabulka": "AMC_PropertyTrigger",
			"Primární klíč": "PropertyTriggerID",
			"Třída": "Alstanet.Library.Web.AMC.Business.PropertyTrigger, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.PropertyTrigger",
			"Modul": "Administrace"
		},
		{
			"ID": "4259301",
			"Název CZ": "Slovník",
			"Tabulka": "AMC_LangDictionary",
			"Primární klíč": "LangDictionaryID",
			"Třída": "Alstanet.Library.Web.AMC.Business.LangDictionary, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.LangDictionary",
			"Modul": "Administrace"
		},
		{
			"ID": "4262102",
			"Název CZ": "Stav pneumatik",
			"Tabulka": "FM_CF_TireCondition",
			"Primární klíč": "TireConditionID",
			"Třída": "Alstanet.Fm.Cf.Business.TireCondition, App_Code",
			"Aplikační objekt": "FM.CF.TireCondition",
			"Modul": "Autopark"
		},
		{
			"ID": "4286251",
			"Název CZ": "Typ události",
			"Tabulka": "AMC_CustActionEventType",
			"Primární klíč": "CustActionEventTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustActionEventType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustActionEventType",
			"Modul": "Administrace"
		},
		{
			"ID": "4304606",
			"Název CZ": "Druh",
			"Tabulka": "FM_TCH_Mode",
			"Primární klíč": "ModeID",
			"Třída": "Alstanet.Fm.Tch.Business.Mode, App_Code",
			"Aplikační objekt": "FM.TCH.Mode",
			"Modul": "Technologie"
		},
		{
			"ID": "4318514",
			"Název CZ": "Perioda odpisů",
			"Tabulka": "FM_AST_DepreciationPeriodType",
			"Primární klíč": "DepreciationPeriodTypeID",
			"Třída": "Alstanet.Fm.Ast.Business.DepreciationPeriodType, App_Code",
			"Aplikační objekt": "FM.AST.DepreciationPeriodType",
			"Modul": "Majetek"
		},
		{
			"ID": "4350044",
			"Název CZ": "Rozpadový klíč",
			"Tabulka": "FM_CO_DistributionKey",
			"Primární klíč": "DistributionKeyID",
			"Třída": "Alstanet.Fm.Co.Business.DistributionKey, App_Code",
			"Aplikační objekt": "FM.CO.DistributionKey",
			"Modul": "Controlling"
		},
		{
			"ID": "4350260",
			"Název CZ": "Druh majetku",
			"Tabulka": "FM_AST_AssetKind",
			"Primární klíč": "AssetKindID",
			"Třída": "Alstanet.Fm.Ast.Business.AssetKind, App_Code",
			"Aplikační objekt": "FM.AST.AssetKind",
			"Modul": "Majetek"
		},
		{
			"ID": "4375421",
			"Název CZ": "Typ nákladu",
			"Tabulka": "FM_IVO_CostType",
			"Primární klíč": "CostTypeID",
			"Třída": "Alstanet.Fm.Ivo.Business.CostType, App_Code",
			"Aplikační objekt": "FM.IVO.CostType",
			"Modul": "Faktury"
		},
		{
			"ID": "4393497",
			"Název CZ": "Hodnota sazby DPH",
			"Tabulka": "FM_FI_VatRateItem",
			"Primární klíč": "VatRateItemID",
			"Třída": "Alstanet.Fm.Fi.Business.VatRateItem, App_Code",
			"Aplikační objekt": "FM.FI.VatRateItem",
			"Modul": "Ekonomika"
		},
		{
			"ID": "4417398",
			"Název CZ": "Predikce spotřeby",
			"Tabulka": "FM_ENG_ACEPrediction",
			"Primární klíč": "ACEPredictionID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEPrediction, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEPrediction",
			"Modul": "Energie"
		},
		{
			"ID": "4445190",
			"Název CZ": "Složka",
			"Tabulka": "FM_DMS_Folder",
			"Primární klíč": "FolderID",
			"Třída": "Alstanet.Fm.Dms.Business.Folder, App_Code",
			"Aplikační objekt": "FM.DMS.Folder",
			"Modul": "DMS"
		},
		{
			"ID": "4481795",
			"Název CZ": "Uživatel",
			"Tabulka": "AMC_User",
			"Primární klíč": "UserID",
			"Třída": "Alstanet.Library.Web.AMC.Business.User, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.User",
			"Modul": "Administrace"
		},
		{
			"ID": "4492953",
			"Název CZ": "Typ objednávky",
			"Tabulka": "FM_ORD_OrderType",
			"Primární klíč": "OrderTypeID",
			"Třída": "Alstanet.Fm.Ord.Business.OrderType, App_Code",
			"Aplikační objekt": "FM.ORD.OrderType",
			"Modul": "Objednávky"
		},
		{
			"ID": "4497267",
			"Název CZ": "Četnost",
			"Tabulka": "FM_PSP_Frequency",
			"Primární klíč": "FrequencyID",
			"Třída": "Alstanet.Fm.Psp.Business.Frequency, App_Code",
			"Aplikační objekt": "FM.PSP.Frequency",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "4518586",
			"Název CZ": "Příloha",
			"Tabulka": "AMC_Attachment",
			"Primární klíč": "AttachmentID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Attachment, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Attachment",
			"Modul": "Administrace"
		},
		{
			"ID": "4536287",
			"Název CZ": "Karta",
			"Tabulka": "FM_KCM_Card",
			"Primární klíč": "CardID",
			"Třída": "Alstanet.Fm.Kcm.Business.Card, App_Code",
			"Aplikační objekt": "FM.KCM.Card",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "4565574",
			"Název CZ": "Systémový objekt",
			"Tabulka": "AMC_SystemObject",
			"Primární klíč": "SystemObjectID",
			"Třída": "Alstanet.Library.Web.AMC.Business.SystemObject, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.SystemObject",
			"Modul": "Administrace"
		},
		{
			"ID": "4572196",
			"Název CZ": "Typ rezervace",
			"Tabulka": "FM_CF_CarReservationType",
			"Primární klíč": "CarReservationTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.CarReservationType, App_Code",
			"Aplikační objekt": "FM.CF.CarReservationType",
			"Modul": "Autopark"
		},
		{
			"ID": "4587855",
			"Název CZ": "Typ osoby",
			"Tabulka": "FM_HR_EmployeeType",
			"Primární klíč": "EmployeeTypeID",
			"Třída": "Alstanet.Fm.Hr.Business.EmployeeType, App_Code",
			"Aplikační objekt": "FM.HR.EmployeeType",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "4587876",
			"Název CZ": "Ceník elektřiny",
			"Tabulka": "FM_CF_ElectricityPriceList",
			"Primární klíč": "ElectricityPriceListID",
			"Třída": "Alstanet.Fm.Cf.Business.ElectricityPriceList, App_Code",
			"Aplikační objekt": "FM.CF.ElectricityPriceList",
			"Modul": "Autopark"
		},
		{
			"ID": "4625771",
			"Název CZ": "Spotřeba CR360",
			"Tabulka": "FM_ENG_ACEConsumptionCR360",
			"Primární klíč": "ACEConsumptionCR360ID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEConsumptionCR360, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEConsumptionCR360",
			"Modul": "Energie"
		},
		{
			"ID": "4695396",
			"Název CZ": "Spustit na",
			"Tabulka": "AMC_ActionEntityType",
			"Primární klíč": "ActionEntityTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ActionEntityType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ActionEntityType",
			"Modul": "Administrace"
		},
		{
			"ID": "4713410",
			"Název CZ": "Osoba u entity",
			"Tabulka": "FM_HR_Employee_Entity",
			"Primární klíč": "Employee_EntityID",
			"Třída": "Alstanet.Fm.Hr.Business.Employee_Entity, App_Code",
			"Aplikační objekt": "FM.HR.Employee_Entity",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "4714419",
			"Název CZ": "Schvalování vyřazovaného majetku",
			"Tabulka": "FM_AST_DisposalApproveItem",
			"Primární klíč": "DisposalApproveItemID",
			"Třída": "Alstanet.Fm.Ast.Business.DisposalApproveItem, App_Code",
			"Aplikační objekt": "FM.AST.DisposalApproveItem",
			"Modul": "Majetek"
		},
		{
			"ID": "4746080",
			"Název CZ": "Typ položky",
			"Tabulka": "FM_IVO_InvoiceItemType",
			"Primární klíč": "InvoiceItemTypeID",
			"Třída": "Alstanet.Fm.Ivo.Business.InvoiceItemType, App_Code",
			"Aplikační objekt": "FM.IVO.InvoiceItemType",
			"Modul": "Faktury"
		},
		{
			"ID": "4805021",
			"Název CZ": "Inventární úsek",
			"Tabulka": "FM_AST_InventorySection",
			"Primární klíč": "InventorySectionID",
			"Třída": "Alstanet.Fm.Ast.Business.InventorySection, App_Code",
			"Aplikační objekt": "FM.AST.InventorySection",
			"Modul": "Majetek"
		},
		{
			"ID": "4805038",
			"Název CZ": "Sestava",
			"Tabulka": "FM_INV_Action",
			"Primární klíč": "ActionID",
			"Třída": "Alstanet.Fm.Inv.Business.Action, App_Code",
			"Aplikační objekt": "FM.INV.Action",
			"Modul": "Inventury"
		},
		{
			"ID": "4810558",
			"Název CZ": "Typ zprávy",
			"Tabulka": "FM_WF_MessageType",
			"Primární klíč": "MessageTypeID",
			"Třída": "Alstanet.Fm.Wf.Business.MessageType, App_Code",
			"Aplikační objekt": "FM.WF.MessageType",
			"Modul": "Workflow"
		},
		{
			"ID": "4811056",
			"Název CZ": "Část obce",
			"Tabulka": "FM_PSP_CityPart",
			"Primární klíč": "CityPartID",
			"Třída": "Alstanet.Fm.Psp.Business.CityPart, App_Code",
			"Aplikační objekt": "FM.PSP.CityPart",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "4827390",
			"Název CZ": "Závada",
			"Tabulka": "FM_MNT_Fault",
			"Primární klíč": "FaultID",
			"Třída": "Alstanet.Fm.Mnt.Business.Fault, App_Code",
			"Aplikační objekt": "FM.MNT.Fault",
			"Modul": "Údržba"
		},
		{
			"ID": "4831150",
			"Název CZ": "Prostory v jednotce",
			"Tabulka": "FM_PSP_UnitGroupZone",
			"Primární klíč": "UnitGroupZoneID",
			"Třída": "Alstanet.Fm.Psp.Business.UnitGroupZone, App_Code",
			"Aplikační objekt": "FM.PSP.UnitGroupZone",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "4849991",
			"Název CZ": "Úkon",
			"Tabulka": "FM_FI_Operation",
			"Primární klíč": "OperationID",
			"Třída": "Alstanet.Fm.Fi.Business.Operation, App_Code",
			"Aplikační objekt": "FM.FI.Operation",
			"Modul": "Ekonomika"
		},
		{
			"ID": "4874137",
			"Název CZ": "Země",
			"Tabulka": "AMC_Country",
			"Primární klíč": "CountryID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Country, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Country",
			"Modul": "Administrace"
		},
		{
			"ID": "4891812",
			"Název CZ": "Aplikační konfigurace",
			"Tabulka": "Amc_Config",
			"Primární klíč": "ConfigID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Config, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Config2",
			"Modul": "Administrace"
		},
		{
			"ID": "4895965",
			"Název CZ": "Druh energie",
			"Tabulka": "FM_ENG_EnergyType",
			"Primární klíč": "EnergyTypeID",
			"Třída": "Alstanet.Fm.Eng.Business.EnergyType, App_Code",
			"Aplikační objekt": "FM.ENG.EnergyType",
			"Modul": "Energie"
		},
		{
			"ID": "4983352",
			"Název CZ": "Typ faktury",
			"Tabulka": "FM_IVO_InvoiceType",
			"Primární klíč": "InvoiceTypeID",
			"Třída": "Alstanet.Fm.Ivo.Business.InvoiceType, App_Code",
			"Aplikační objekt": "FM.IVO.InvoiceType",
			"Modul": "Faktury"
		},
		{
			"ID": "5011003",
			"Název CZ": "Zátěž hlavního jističe",
			"Tabulka": "FM_ENG_MainCircBreakLoad",
			"Primární klíč": "MainCircBreakLoadID",
			"Třída": "Alstanet.Fm.Eng.Business.MainCircBreakLoad, App_Code",
			"Aplikační objekt": "FM.ENG.MainCircBreakLoad",
			"Modul": "Energie"
		},
		{
			"ID": "5056309",
			"Název CZ": "Kvalifikace",
			"Tabulka": "FM_HR_Qualification",
			"Primární klíč": "QualificationID",
			"Třída": "Alstanet.Fm.Hr.Business.Qualification, App_Code",
			"Aplikační objekt": "FM.HR.Qualification",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "5059209",
			"Název CZ": "Typ notifikace",
			"Tabulka": "AMC_NotificationType",
			"Primární klíč": "NotificationTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.NotificationType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.NotificationType",
			"Modul": "Administrace"
		},
		{
			"ID": "5070289",
			"Název CZ": "Úroveň subjektu",
			"Tabulka": "FM_HR_SubjectDepth",
			"Primární klíč": "SubjectDepthID",
			"Třída": "Alstanet.Fm.Hr.Business.SubjectDepth, App_Code",
			"Aplikační objekt": "FM.HR.SubjectDepth",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "5073984",
			"Název CZ": "Položka nabídky",
			"Tabulka": "FM_ORD_OfferItem",
			"Primární klíč": "OfferItemID",
			"Třída": "Alstanet.Fm.Ord.Business.OfferItem, App_Code",
			"Aplikační objekt": "FM.ORD.OfferItem",
			"Modul": "Objednávky"
		},
		{
			"ID": "5080626",
			"Název CZ": "Položka překladu číselníku",
			"Tabulka": "AMC_EnumTranslationItem",
			"Primární klíč": "EnumTranslationItemID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EnumTranslationItem, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.EnumTranslationItem",
			"Modul": "Administrace"
		},
		{
			"ID": "5096993",
			"Název CZ": "Autoprovoz",
			"Tabulka": "FM_CF_CarPark",
			"Primární klíč": "CarParkID",
			"Třída": "Alstanet.Fm.Cf.Business.CarPark, App_Code",
			"Aplikační objekt": "FM.CF.CarPark",
			"Modul": "Autopark"
		},
		{
			"ID": "5102358",
			"Název CZ": "Upomínka",
			"Tabulka": "FM_IVO_Reminder",
			"Primární klíč": "ReminderID",
			"Třída": "Alstanet.Fm.Ivo.Business.Reminder, App_Code",
			"Aplikační objekt": "FM.IVO.Reminder",
			"Modul": "Faktury"
		},
		{
			"ID": "5114672",
			"Název CZ": "Členství skupiny ve skupinách",
			"Tabulka": "AMC_GroupMemberOfGroup",
			"Primární klíč": "GroupMemberOfGroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.GroupMemberOfGroup, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.GroupMemberOfGroup",
			"Modul": "Administrace"
		},
		{
			"ID": "5134618",
			"Název CZ": "Inventarizovaný majetek",
			"Tabulka": "FM_INV_Asset",
			"Primární klíč": "AssetID",
			"Třída": "Alstanet.Fm.Inv.Business.Asset, App_Code",
			"Aplikační objekt": "FM.INV.Asset",
			"Modul": "Inventury"
		},
		{
			"ID": "5135646",
			"Název CZ": "Sledovaná položka",
			"Tabulka": "AMC_EventLogProperty",
			"Primární klíč": "EventLogPropertyID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogProperty, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogProperty",
			"Modul": "Administrace"
		},
		{
			"ID": "5177922",
			"Název CZ": "Uživatelská akce",
			"Tabulka": "AMC_CustAction",
			"Primární klíč": "CustActionID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustAction, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustAction",
			"Modul": "Administrace"
		},
		{
			"ID": "5181397",
			"Název CZ": "Typ checklistu",
			"Tabulka": "FM_MNT_ChecklistType",
			"Primární klíč": "ChecklistTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.ChecklistType, App_Code",
			"Aplikační objekt": "FM.MNT.ChecklistType",
			"Modul": "Údržba"
		},
		{
			"ID": "5184046",
			"Název CZ": "Typ spotřeby",
			"Tabulka": "FM_CF_FuelConsAvgType",
			"Primární klíč": "FuelConsAvgTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.FuelConsAvgType, App_Code",
			"Aplikační objekt": "FM.CF.FuelConsAvgType",
			"Modul": "Autopark"
		},
		{
			"ID": "5192678",
			"Název CZ": "Opce",
			"Tabulka": "FM_CM_Option",
			"Primární klíč": "OptionID",
			"Třída": "Alstanet.Fm.Cm.Business.Option, App_Code",
			"Aplikační objekt": "FM.CM.Option",
			"Modul": "Smlouvy"
		},
		{
			"ID": "5206204",
			"Název CZ": "Tovární značka",
			"Tabulka": "FM_CF_Producer",
			"Primární klíč": "ProducerID",
			"Třída": "Alstanet.Fm.Cf.Business.Producer, App_Code",
			"Aplikační objekt": "FM.CF.Producer",
			"Modul": "Autopark"
		},
		{
			"ID": "5228989",
			"Název CZ": "Způsob užití vozidla",
			"Tabulka": "FM_CF_CarDesignation",
			"Primární klíč": "CarDesignationID",
			"Třída": "Alstanet.Fm.Cf.Business.CarDesignation, App_Code",
			"Aplikační objekt": "FM.CF.CarDesignation",
			"Modul": "Autopark"
		},
		{
			"ID": "5310341",
			"Název CZ": "Technické zhodnocení",
			"Tabulka": "FM_AST_TechnicalAppreciation",
			"Primární klíč": "TechnicalAppreciationID",
			"Třída": "Alstanet.Fm.Ast.Business.TechnicalAppreciation, App_Code",
			"Aplikační objekt": "FM.AST.TechnicalAppreciation",
			"Modul": "Majetek"
		},
		{
			"ID": "5320850",
			"Název CZ": "Jednotka posunu",
			"Tabulka": "AMC_ScheduleOffsetTimeUnit",
			"Primární klíč": "ScheduleOffsetTimeUnitID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ScheduleOffsetTimeUnit, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ScheduleOffsetTimeUnit",
			"Modul": "Administrace"
		},
		{
			"ID": "5325876",
			"Název CZ": "Stav vlastnictví",
			"Tabulka": "FM_PSP_ACEOwnershipStatus",
			"Primární klíč": "ACEOwnershipStatusID",
			"Třída": "Alstanet.Fm.Psp.Business.ACEOwnershipStatus, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACEOwnershipStatus",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5341582",
			"Název CZ": "Typ stavby",
			"Tabulka": "FM_PSP_BuildingType2",
			"Primární klíč": "BuildingType2ID",
			"Třída": "Alstanet.Fm.Psp.Business.BuildingType2, App_Code",
			"Aplikační objekt": "",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5346666",
			"Název CZ": "Zaměstnanec na šabloně schvalovatele",
			"Tabulka": "FM_WF_ApproverTemplateItem",
			"Primární klíč": "ApproverTemplateItemID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproverTemplateItem, App_Code",
			"Aplikační objekt": "FM.WF.ApproverTemplateItem",
			"Modul": "Workflow"
		},
		{
			"ID": "5370889",
			"Název CZ": "Obec",
			"Tabulka": "FM_PSP_City",
			"Primární klíč": "CityID",
			"Třída": "Alstanet.Fm.Psp.Business.City, App_Code",
			"Aplikační objekt": "FM.PSP.City",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5372185",
			"Název CZ": "Typ doplňkové platby",
			"Tabulka": "FM_CF_AdditionalPaymentType",
			"Primární klíč": "AdditionalPaymentTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.AdditionalPaymentType, App_Code",
			"Aplikační objekt": "FM.CF.AdditionalPaymentType",
			"Modul": "Autopark"
		},
		{
			"ID": "5388075",
			"Název CZ": "Kategorie pobočky",
			"Tabulka": "FM_PSP_ACEBranchCategory",
			"Primární klíč": "ACEBranchCategoryID",
			"Třída": "Alstanet.Fm.Psp.Business.ACEBranchCategory, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACEBranchCategory",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5415136",
			"Název CZ": "Den v týdnu",
			"Tabulka": "AMC_WeekdayEnum",
			"Primární klíč": "WeekdayEnumID",
			"Třída": "Alstanet.Library.Web.AMC.Business.WeekdayEnum, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.WeekdayEnum",
			"Modul": "Administrace"
		},
		{
			"ID": "5416920",
			"Název CZ": "Spotřeba hyg. materiálu",
			"Tabulka": "FM_PSP_SourceConsumption",
			"Primární klíč": "SourceConsumptionID",
			"Třída": "Alstanet.Fm.Psp.Business.SourceConsumption, App_Code",
			"Aplikační objekt": "FM.PSP.SourceConsumption",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5438610",
			"Název CZ": "Typ šablony schvalování",
			"Tabulka": "FM_WF_ApproveTemplateType",
			"Primární klíč": "ApproveTemplateTypeID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproveTemplateType, App_Code",
			"Aplikační objekt": "FM.WF.ApproveTemplateType",
			"Modul": "Workflow"
		},
		{
			"ID": "5453389",
			"Název CZ": "Typ nemovitosti",
			"Tabulka": "FM_PSP_BuildingType",
			"Primární klíč": "BuildingTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.BuildingType, App_Code",
			"Aplikační objekt": "FM.PSP.BuildingType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5453495",
			"Název CZ": "Typ entity výkresu",
			"Tabulka": "FM_CAD_DrawingEntityType",
			"Primární klíč": "DrawingEntityTypeID",
			"Třída": "Alstanet.Fm.Cad.Business.DrawingEntityType, App_Code",
			"Aplikační objekt": "FM.CAD.DrawingEntityType",
			"Modul": "Vizualizace"
		},
		{
			"ID": "5473879",
			"Název CZ": "Povrchová úprava",
			"Tabulka": "FM_TCH_SurfaceTreatment",
			"Primární klíč": "SurfaceTreatmentID",
			"Třída": "Alstanet.Fm.Tch.Business.SurfaceTreatment, App_Code",
			"Aplikační objekt": "FM.TCH.SurfaceTreatment",
			"Modul": "Technologie"
		},
		{
			"ID": "5477140",
			"Název CZ": "Pokus o přihlášení",
			"Tabulka": "AMC_UserLogin",
			"Primární klíč": "UserLoginID",
			"Třída": "Alstanet.Library.Web.AMC.Business.UserLogin, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.UserLogin",
			"Modul": "Administrace"
		},
		{
			"ID": "5517621",
			"Název CZ": "Činnost u subjektu",
			"Tabulka": "FM_HR_Subject_Activity",
			"Primární klíč": "Subject_ActivityID",
			"Třída": "Alstanet.Fm.Hr.Business.Subject_Activity, App_Code",
			"Aplikační objekt": "FM.HR.Subject_Activity",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "5535570",
			"Název CZ": "Kód nakládání",
			"Tabulka": "ACM_WST_ACEWasteActivity",
			"Primární klíč": "ACEWasteActivityID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteActivity, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteActivity",
			"Modul": "Odpady"
		},
		{
			"ID": "5536151",
			"Název CZ": "Priorita",
			"Tabulka": "AMC_EmailLogPriority",
			"Primární klíč": "EmailLogPriorityID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailLogPriority, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailLogPriority",
			"Modul": "Administrace"
		},
		{
			"ID": "5551070",
			"Název CZ": "Řešitelský tým",
			"Tabulka": "FM_MNT_Team",
			"Primární klíč": "TeamID",
			"Třída": "Alstanet.Fm.Mnt.Business.Team, App_Code",
			"Aplikační objekt": "FM.MNT.Team",
			"Modul": "Údržba"
		},
		{
			"ID": "5601947",
			"Název CZ": "Náklad",
			"Tabulka": "FM_IVO_Cost",
			"Primární klíč": "CostID",
			"Třída": "Alstanet.Fm.Ivo.Business.Cost, App_Code",
			"Aplikační objekt": "FM.IVO.Cost",
			"Modul": "Faktury"
		},
		{
			"ID": "5610149",
			"Název CZ": "Rozpad přes",
			"Tabulka": "FM_IVO_DistributionOver",
			"Primární klíč": "DistributionOverID",
			"Třída": "Alstanet.Fm.Ivo.Business.DistributionOver, App_Code",
			"Aplikační objekt": "FM.IVO.DistributionOver",
			"Modul": "Faktury"
		},
		{
			"ID": "5628747",
			"Název CZ": "Přidělení parkovacího místa",
			"Tabulka": "FM_CF_ParkPlaceAssign",
			"Primární klíč": "ParkPlaceAssignID",
			"Třída": "Alstanet.Fm.Cf.Business.ParkPlaceAssign, App_Code",
			"Aplikační objekt": "FM.CF.ParkPlaceAssign",
			"Modul": "Autopark"
		},
		{
			"ID": "5643578",
			"Název CZ": "Katalog odpadů",
			"Tabulka": "ACM_WST_ACEWasteCatalogue",
			"Primární klíč": "ACEWasteCatalogueID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteCatalogue, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteCatalogue",
			"Modul": "Odpady"
		},
		{
			"ID": "5666844",
			"Název CZ": "1/4 hodinové maximum",
			"Tabulka": "FM_ENG_HourMax",
			"Primární klíč": "HourMaxID",
			"Třída": "Alstanet.Fm.Eng.Business.HourMax, App_Code",
			"Aplikační objekt": "FM.ENG.HourMax",
			"Modul": "Energie"
		},
		{
			"ID": "5729475",
			"Název CZ": "Typ překročení",
			"Tabulka": "FM_MNT_PenaltyType",
			"Primární klíč": "PenaltyTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.PenaltyType, App_Code",
			"Aplikační objekt": "FM.MNT.PenaltyType",
			"Modul": "Údržba"
		},
		{
			"ID": "5732940",
			"Název CZ": "Typ jednání",
			"Tabulka": "FM_HR_MeetingType",
			"Primární klíč": "MeetingTypeID",
			"Třída": "Alstanet.Fm.Hr.Business.MeetingType, App_Code",
			"Aplikační objekt": "FM.HR.MeetingType",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "5743780",
			"Název CZ": "Typ odkazu",
			"Tabulka": "FM_CMS_UrlType",
			"Primární klíč": "UrlTypeID",
			"Třída": "Alstanet.Fm.Cms.Business.UrlType, App_Code",
			"Aplikační objekt": "FM.CMS.UrlType",
			"Modul": "Portál"
		},
		{
			"ID": "5744051",
			"Název CZ": "Využití nemovitosti",
			"Tabulka": "FM_PSP_BuildingUsage",
			"Primární klíč": "BuildingUsageID",
			"Třída": "Alstanet.Fm.Psp.Business.BuildingUsage, App_Code",
			"Aplikační objekt": "FM.PSP.BuildingUsage",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5756662",
			"Název CZ": "Bezpečnostní objekt",
			"Tabulka": "AMC_SecurityObject",
			"Primární klíč": "SecurityObjectID",
			"Třída": "Alstanet.Library.Web.AMC.Business.SecurityObject, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.SecurityObject",
			"Modul": "Administrace"
		},
		{
			"ID": "5761604",
			"Název CZ": "Druh dokladu",
			"Tabulka": "FM_IVO_InvoiceKind",
			"Primární klíč": "InvoiceKindID",
			"Třída": "Alstanet.Fm.Ivo.Business.InvoiceKind, App_Code",
			"Aplikační objekt": "FM.IVO.InvoiceKind",
			"Modul": "Faktury"
		},
		{
			"ID": "5780760",
			"Název CZ": "Číslo materiálu",
			"Tabulka": "FM_FI_MaterialNumber",
			"Primární klíč": "MaterialNumberID",
			"Třída": "Alstanet.Fm.Fi.Business.MaterialNumber, App_Code",
			"Aplikační objekt": "FM.FI.MaterialNumber",
			"Modul": "Ekonomika"
		},
		{
			"ID": "5782557",
			"Název CZ": "Projekt",
			"Tabulka": "FM_FI_Project",
			"Primární klíč": "ProjectID",
			"Třída": "Alstanet.Fm.Fi.Business.Project, App_Code",
			"Aplikační objekt": "FM.FI.Project",
			"Modul": "Ekonomika"
		},
		{
			"ID": "5784435",
			"Název CZ": "CTRL Hierarchie",
			"Tabulka": "FM_FI_ACECtrlHierarchy",
			"Primární klíč": "ACECtrlHierarchyID",
			"Třída": "Alstanet.Fm.Fi.Business.ACECtrlHierarchy, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.FI.ACECtrlHierarchy",
			"Modul": "Ekonomika"
		},
		{
			"ID": "5800772",
			"Název CZ": "Upomínaný doklad",
			"Tabulka": "FM_IVO_RemindedInvoice",
			"Primární klíč": "RemindedInvoiceID",
			"Třída": "Alstanet.Fm.Ivo.Business.RemindedInvoice, App_Code",
			"Aplikační objekt": "FM.IVO.RemindedInvoice",
			"Modul": "Faktury"
		},
		{
			"ID": "5818973",
			"Název CZ": "Sezóna",
			"Tabulka": "FM_CF_TireSeason",
			"Primární klíč": "TireSeasonID",
			"Třída": "Alstanet.Fm.Cf.Business.TireSeason, App_Code",
			"Aplikační objekt": "FM.CF.TireSeason",
			"Modul": "Autopark"
		},
		{
			"ID": "5822144",
			"Název CZ": "Typ zprávy",
			"Tabulka": "AMC_MessageLogParentType",
			"Primární klíč": "MessageLogParentTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.MessageLogParentType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.MessageLogParentType",
			"Modul": "Administrace"
		},
		{
			"ID": "5835359",
			"Název CZ": "Typ indexu",
			"Tabulka": "FM_CM_PriceIndexType",
			"Primární klíč": "PriceIndexTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.PriceIndexType, App_Code",
			"Aplikační objekt": "FM.CM.PriceIndexType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "5839814",
			"Název CZ": "Povrch stropu",
			"Tabulka": "FM_PSP_CeilingSurface",
			"Primární klíč": "CeilingSurfaceID",
			"Třída": "Alstanet.Fm.Psp.Business.CeilingSurface, App_Code",
			"Aplikační objekt": "FM.PSP.CeilingSurface",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "5877476",
			"Název CZ": "Typ atributu",
			"Tabulka": "AMC_CustPropertyType",
			"Primární klíč": "CustPropertyTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustPropertyType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustPropertyType",
			"Modul": "Administrace"
		},
		{
			"ID": "5885603",
			"Název CZ": "Hlavní jistič",
			"Tabulka": "FM_ENG_MainCircBreak",
			"Primární klíč": "MainCircBreakID",
			"Třída": "Alstanet.Fm.Eng.Business.MainCircBreak, App_Code",
			"Aplikační objekt": "FM.ENG.MainCircBreak",
			"Modul": "Energie"
		},
		{
			"ID": "5887519",
			"Název CZ": "Souřadnicový systém",
			"Tabulka": "FM_CDS_ParcelCoordinateSystem",
			"Primární klíč": "ParcelCoordinateSystemID",
			"Třída": "Alstanet.Fm.Cds.Business.ParcelCoordinateSystem, App_Code",
			"Aplikační objekt": "FM.CDS.ParcelCoordinateSystem",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "5895399",
			"Název CZ": "Bankovní účet",
			"Tabulka": "FM_HR_BankAccount",
			"Primární klíč": "BankAccountID",
			"Třída": "Alstanet.Fm.Hr.Business.BankAccount, App_Code",
			"Aplikační objekt": "FM.HR.BankAccount",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "5926655",
			"Název CZ": "Ceník",
			"Tabulka": "FM_FI_SourcePriceItem",
			"Primární klíč": "SourcePriceItemID",
			"Třída": "Alstanet.Fm.Fi.Business.SourcePriceItem, App_Code",
			"Aplikační objekt": "FM.FI.SourcePriceItem",
			"Modul": "Ekonomika"
		},
		{
			"ID": "5935067",
			"Název CZ": "Provozovna",
			"Tabulka": "ACM_WST_ACEWasteBuilding",
			"Primární klíč": "ACEWasteBuildingID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteBuilding, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteBuilding",
			"Modul": "Odpady"
		},
		{
			"ID": "5955599",
			"Název CZ": "Typ zařízení",
			"Tabulka": "ACM_WST_ACEWasteBuildingType",
			"Primární klíč": "ACEWasteBuildingTypeID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteBuildingType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteBuildingType",
			"Modul": "Odpady"
		},
		{
			"ID": "5961854",
			"Název CZ": "Klíč",
			"Tabulka": "FM_KCM_Key",
			"Primární klíč": "KeyID",
			"Třída": "Alstanet.Fm.Kcm.Business.Key, App_Code",
			"Aplikační objekt": "FM.KCM.Key",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "5967524",
			"Název CZ": "Scope",
			"Tabulka": "AMC_OAuthScope",
			"Primární klíč": "OAuthScopeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.OAuthScope, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.OAuthScope",
			"Modul": "Administrace"
		},
		{
			"ID": "5988291",
			"Název CZ": "Obrázek",
			"Tabulka": "AMC_HelpArticlePicture",
			"Primární klíč": "HelpArticlePictureID",
			"Třída": "Alstanet.Library.Web.AMC.Business.HelpArticlePicture, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.HelpArticlePicture",
			"Modul": "Administrace"
		},
		{
			"ID": "5992532",
			"Název CZ": "Původ parcely",
			"Tabulka": "FM_CDS_ParcelOrigin",
			"Primární klíč": "ParcelOriginID",
			"Třída": "Alstanet.Fm.Cds.Business.ParcelOrigin, App_Code",
			"Aplikační objekt": "FM.CDS.ParcelOrigin",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "6017847",
			"Název CZ": "Uživatelská konfigurace",
			"Tabulka": "AMC_ApplObjectConfig",
			"Primární klíč": "ApplObjectConfigID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ApplObjectConfig, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ApplObjectConfig",
			"Modul": "Administrace"
		},
		{
			"ID": "6045454",
			"Název CZ": "Přidělení vozidla",
			"Tabulka": "FM_CF_Assign",
			"Primární klíč": "AssignID",
			"Třída": "Alstanet.Fm.Cf.Business.Assign, App_Code",
			"Aplikační objekt": "FM.CF.Assign",
			"Modul": "Autopark"
		},
		{
			"ID": "6114166",
			"Název CZ": "Způsob placení",
			"Tabulka": "FM_CF_PaymentType",
			"Primární klíč": "PaymentTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.PaymentType, App_Code",
			"Aplikační objekt": "FM.CF.PaymentType",
			"Modul": "Autopark"
		},
		{
			"ID": "6169778",
			"Název CZ": "Okres",
			"Tabulka": "FM_PSP_District",
			"Primární klíč": "DistrictID",
			"Třída": "Alstanet.Fm.Psp.Business.District, App_Code",
			"Aplikační objekt": "FM.PSP.District",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "6170563",
			"Název CZ": "Emailová předloha",
			"Tabulka": "AMC_EmailTemplate",
			"Primární klíč": "EmailTemplateID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailTemplate, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailTemplate",
			"Modul": "Administrace"
		},
		{
			"ID": "6201036",
			"Název CZ": "Typ podmíněného použití",
			"Tabulka": "AMC_PropertyTriggerParentType",
			"Primární klíč": "PropertyTriggerParentTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.PropertyTriggerParentType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.PropertyTriggerParentType",
			"Modul": "Administrace"
		},
		{
			"ID": "6210085",
			"Název CZ": "Skupina typů akce",
			"Tabulka": "AMC_CustActionTypeGroup",
			"Primární klíč": "CustActionTypeGroupID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustActionTypeGroup, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustActionTypeGroup",
			"Modul": "Administrace"
		},
		{
			"ID": "6239528",
			"Název CZ": "Typ uživatelské entity",
			"Tabulka": "AMC_CustEntityBaseType",
			"Primární klíč": "CustEntityBaseTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustEntityBaseType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "",
			"Modul": "Administrace"
		},
		{
			"ID": "6242736",
			"Název CZ": "Organizační jednotka",
			"Tabulka": "FM_HR_OrgUnit",
			"Primární klíč": "OrgUnitID",
			"Třída": "Alstanet.Fm.Hr.Business.OrgUnit, App_Code",
			"Aplikační objekt": "FM.HR.OrgUnit",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "6243235",
			"Název CZ": "Stav jednání",
			"Tabulka": "FM_HR_MeetingStatus",
			"Primární klíč": "MeetingStatusID",
			"Třída": "Alstanet.Fm.Hr.Business.MeetingStatus, App_Code",
			"Aplikační objekt": "FM.HR.MeetingStatus",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "6258761",
			"Název CZ": "Stav zprávy",
			"Tabulka": "AMC_MessageLogStatus",
			"Primární klíč": "MessageLogStatusID",
			"Třída": "Alstanet.Library.Web.AMC.Business.MessageLogStatus, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.MessageLogStatus",
			"Modul": "Administrace"
		},
		{
			"ID": "6268506",
			"Název CZ": "Použití přiděleného vozidla",
			"Tabulka": "FM_CF_AssignUsage",
			"Primární klíč": "AssignUsageID",
			"Třída": "Alstanet.Fm.Cf.Business.AssignUsage, App_Code",
			"Aplikační objekt": "FM.CF.AssignUsage",
			"Modul": "Autopark"
		},
		{
			"ID": "6287194",
			"Název CZ": "Obchodní místo",
			"Tabulka": "FM_PSP_ACEBusinessPlace",
			"Primární klíč": "ACEBusinessPlaceID",
			"Třída": "Alstanet.Fm.Psp.Business.ACEBusinessPlace, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACEBusinessPlace",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "6287571",
			"Název CZ": "Kraj",
			"Tabulka": "FM_PSP_Province",
			"Primární klíč": "ProvinceID",
			"Třída": "Alstanet.Fm.Psp.Business.Province, App_Code",
			"Aplikační objekt": "FM.PSP.Province",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "6327009",
			"Název CZ": "Typ karty",
			"Tabulka": "FM_CF_CardType",
			"Primární klíč": "CardTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.CardType, App_Code",
			"Aplikační objekt": "FM.CF.CardType",
			"Modul": "Autopark"
		},
		{
			"ID": "6340138",
			"Název CZ": "Uhlíkový offset",
			"Tabulka": "ACM_ESG_ACEEmissionOffset",
			"Primární klíč": "ACEEmissionOffsetID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEmissionOffset, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEmissionOffset",
			"Modul": "ESG"
		},
		{
			"ID": "6385684",
			"Název CZ": "Inventura",
			"Tabulka": "FM_INV_Inventory",
			"Primární klíč": "InventoryID",
			"Třída": "Alstanet.Fm.Inv.Business.Inventory, App_Code",
			"Aplikační objekt": "FM.INV.Inventory",
			"Modul": "Inventury"
		},
		{
			"ID": "6449371",
			"Název CZ": "Typ likvidace",
			"Tabulka": "FM_CF_LiquidationType",
			"Primární klíč": "LiquidationTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.LiquidationType, App_Code",
			"Aplikační objekt": "FM.CF.LiquidationType",
			"Modul": "Autopark"
		},
		{
			"ID": "6496497",
			"Název CZ": "Zástupná značka",
			"Tabulka": "AMC_EmailTemplateTag",
			"Primární klíč": "EmailTemplateTagID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailTemplateTag, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailTemplateTag",
			"Modul": "Administrace"
		},
		{
			"ID": "6578665",
			"Název CZ": "Podíl na položce dokladu",
			"Tabulka": "FM_IVO_InvoiceItemShare",
			"Primární klíč": "InvoiceItemShareID",
			"Třída": "Alstanet.Fm.Ivo.Business.InvoiceItemShare, App_Code",
			"Aplikační objekt": "FM.IVO.InvoiceItemShare",
			"Modul": "Faktury"
		},
		{
			"ID": "6600663",
			"Název CZ": "Plán emisí",
			"Tabulka": "ACM_ESG_ACEEmissionPlan",
			"Primární klíč": "ACEEmissionPlanID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEmissionPlan, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEmissionPlan",
			"Modul": "ESG"
		},
		{
			"ID": "6646800",
			"Název CZ": "Tarif",
			"Tabulka": "FM_ENG_Tariff",
			"Primární klíč": "TariffID",
			"Třída": "Alstanet.Fm.Eng.Business.Tariff, App_Code",
			"Aplikační objekt": "FM.ENG.Tariff",
			"Modul": "Energie"
		},
		{
			"ID": "6673219",
			"Název CZ": "Přidělení karty",
			"Tabulka": "FM_CF_CardAssign",
			"Primární klíč": "CardAssignID",
			"Třída": "Alstanet.Fm.Cf.Business.CardAssign, App_Code",
			"Aplikační objekt": "FM.CF.CardAssign",
			"Modul": "Autopark"
		},
		{
			"ID": "6674216",
			"Název CZ": "Plánovač",
			"Tabulka": "AMC_Planner",
			"Primární klíč": "PlannerID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Planner, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Planner",
			"Modul": "Administrace"
		},
		{
			"ID": "6685390",
			"Název CZ": "Zdroj majetku",
			"Tabulka": "FM_AST_AssetSource",
			"Primární klíč": "AssetSourceID",
			"Třída": "Alstanet.Fm.Ast.Business.AssetSource, App_Code",
			"Aplikační objekt": "FM.AST.AssetSource",
			"Modul": "Majetek"
		},
		{
			"ID": "6716838",
			"Název CZ": "Typ jednotky",
			"Tabulka": "FM_CDS_UnitGroupType",
			"Primární klíč": "UnitGroupTypeID",
			"Třída": "Alstanet.Fm.Cds.Business.UnitGroupType, App_Code",
			"Aplikační objekt": "FM.CDS.UnitGroupType",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "6735899",
			"Název CZ": "Nákladové středisko",
			"Tabulka": "FM_FI_CostCenter",
			"Primární klíč": "CostCenterID",
			"Třída": "Alstanet.Fm.Fi.Business.CostCenter, App_Code",
			"Aplikační objekt": "FM.FI.CostCenter",
			"Modul": "Ekonomika"
		},
		{
			"ID": "6761127",
			"Název CZ": "Stav úhrady",
			"Tabulka": "FM_IVO_PaymentStatus",
			"Primární klíč": "PaymentStatusID",
			"Třída": "Alstanet.Fm.Ivo.Business.PaymentStatus, App_Code",
			"Aplikační objekt": "FM.IVO.PaymentStatus",
			"Modul": "Faktury"
		},
		{
			"ID": "6825660",
			"Název CZ": "Položka překladu",
			"Tabulka": "AMC_ResourceTranslationItem",
			"Primární klíč": "ResourceTranslationItemID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ResourceTranslationItem, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.ResourceTranslationItem",
			"Modul": "Administrace"
		},
		{
			"ID": "6833646",
			"Název CZ": "Položka energetického mixu",
			"Tabulka": "ACM_ESG_ACEEnergyMixItem",
			"Primární klíč": "ACEEnergyMixItemID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEnergyMixItem, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEnergyMixItem",
			"Modul": "ESG"
		},
		{
			"ID": "6836143",
			"Název CZ": "Umístění odběrného místa",
			"Tabulka": "FM_ENG_ACEPlacePosition",
			"Primární klíč": "ACEPlacePositionID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEPlacePosition, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEPlacePosition",
			"Modul": "Energie"
		},
		{
			"ID": "6870886",
			"Název CZ": "Workflow",
			"Tabulka": "FM_WF_WorkflowType",
			"Primární klíč": "WorkflowTypeID",
			"Třída": "Alstanet.Fm.Wf.Business.WorkflowType, App_Code",
			"Aplikační objekt": "FM.WF.WorkflowType",
			"Modul": "Workflow"
		},
		{
			"ID": "6880904",
			"Název CZ": "Kategorie",
			"Tabulka": "FM_DMS_Category",
			"Primární klíč": "CategoryID",
			"Třída": "Alstanet.Fm.Dms.Business.Category, App_Code",
			"Aplikační objekt": "FM.DMS.Category",
			"Modul": "DMS"
		},
		{
			"ID": "6884253",
			"Název CZ": "Atribut",
			"Tabulka": "AMC_EntityProperty",
			"Primární klíč": "EntityPropertyID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EntityProperty, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EntityProperty",
			"Modul": "Administrace"
		},
		{
			"ID": "6912998",
			"Název CZ": "Položka pravidelné ostrahy",
			"Tabulka": "FM_PSP_SecurityPlanItem",
			"Primární klíč": "SecurityPlanItemID",
			"Třída": "Alstanet.Fm.Psp.Business.SecurityPlanItem, App_Code",
			"Aplikační objekt": "FM.PSP.SecurityPlanItem",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "6938837",
			"Název CZ": "Typ požadavku",
			"Tabulka": "FM_MNT_IncidentType",
			"Primární klíč": "IncidentTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.IncidentType, App_Code",
			"Aplikační objekt": "FM.MNT.IncidentType",
			"Modul": "Údržba"
		},
		{
			"ID": "6977970",
			"Název CZ": "Využití místnosti",
			"Tabulka": "FM_PSP_RoomUsage",
			"Primární klíč": "RoomUsageID",
			"Třída": "Alstanet.Fm.Psp.Business.RoomUsage, App_Code",
			"Aplikační objekt": "FM.PSP.RoomUsage",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "6979773",
			"Název CZ": "Typ lokátoru",
			"Tabulka": "FM_CF_GPSLocatorType",
			"Primární klíč": "GPSLocatorTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.GPSLocatorType, App_Code",
			"Aplikační objekt": "FM.CF.GPSLocatorType",
			"Modul": "Autopark"
		},
		{
			"ID": "7040542",
			"Název CZ": "Výkaz odpadů",
			"Tabulka": "ACM_WST_ACEWasteReport",
			"Primární klíč": "ACEWasteReportID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteReport, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteReport",
			"Modul": "Odpady"
		},
		{
			"ID": "7059296",
			"Název CZ": "Událost karty na PHM",
			"Tabulka": "FM_CF_CardEvent",
			"Primární klíč": "CardEventID",
			"Třída": "Alstanet.Fm.Cf.Business.CardEvent, App_Code",
			"Aplikační objekt": "FM.CF.CardEvent",
			"Modul": "Autopark"
		},
		{
			"ID": "7070362",
			"Název CZ": "Způsob ochrany nemovitosti",
			"Tabulka": "FM_CDS_SecurityMethod",
			"Primární klíč": "SecurityMethodID",
			"Třída": "Alstanet.Fm.Cds.Business.SecurityMethod, App_Code",
			"Aplikační objekt": "FM.CDS.SecurityMethod",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "7099496",
			"Název CZ": "Obrat",
			"Tabulka": "FM_CM_Turnover",
			"Primární klíč": "TurnoverID",
			"Třída": "Alstanet.Fm.Cm.Business.Turnover, App_Code",
			"Aplikační objekt": "FM.CM.Turnover",
			"Modul": "Smlouvy"
		},
		{
			"ID": "7142698",
			"Název CZ": "Přístup k typu entity",
			"Tabulka": "AMC_AccessEntityType",
			"Primární klíč": "AccessEntityTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.AccessEntityType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.AccessEntityType",
			"Modul": "Administrace"
		},
		{
			"ID": "7147264",
			"Název CZ": "Skupina vrstev",
			"Tabulka": "FM_CAD_LayerGroup",
			"Primární klíč": "LayerGroupID",
			"Třída": "Alstanet.Fm.Cad.Business.LayerGroup, App_Code",
			"Aplikační objekt": "FM.CAD.LayerGroup",
			"Modul": "Vizualizace"
		},
		{
			"ID": "7153141",
			"Název CZ": "Emisní faktor",
			"Tabulka": "ACM_ESG_ACEEmissionFactor",
			"Primární klíč": "ACEEmissionFactorID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEEmissionFactor, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEEmissionFactor",
			"Modul": "ESG"
		},
		{
			"ID": "7182396",
			"Název CZ": "Typ emailové předlohy",
			"Tabulka": "AMC_EmailTemplateType",
			"Primární klíč": "EmailTemplateTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EmailTemplateType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EmailTemplateType",
			"Modul": "Administrace"
		},
		{
			"ID": "7198298",
			"Název CZ": "Spotřeba přes místnosti",
			"Tabulka": "FM_ENG_ReadingConsumArea",
			"Primární klíč": "ReadingConsumAreaID",
			"Třída": "Alstanet.Fm.Eng.Business.ReadingConsumArea, App_Code",
			"Aplikační objekt": "FM.ENG.ReadingConsumArea",
			"Modul": "Energie"
		},
		{
			"ID": "7255911",
			"Název CZ": "Stav",
			"Tabulka": "FM_WF_Status",
			"Primární klíč": "StatusID",
			"Třída": "Alstanet.Fm.Wf.Business.Status, App_Code",
			"Aplikační objekt": "FM.WF.Status",
			"Modul": "Workflow"
		},
		{
			"ID": "7256997",
			"Název CZ": "Čtečka",
			"Tabulka": "FM_KCM_CardReader",
			"Primární klíč": "CardReaderID",
			"Třída": "Alstanet.Fm.Kcm.Business.CardReader, App_Code",
			"Aplikační objekt": "FM.KCM.CardReader",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "7273405",
			"Název CZ": "Role osoby",
			"Tabulka": "FM_HR_EmployeeRole",
			"Primární klíč": "EmployeeRoleID",
			"Třída": "Alstanet.Fm.Hr.Business.EmployeeRole, App_Code",
			"Aplikační objekt": "FM.HR.EmployeeRole",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "7293099",
			"Název CZ": "Platba",
			"Tabulka": "FM_IVO_Payment",
			"Primární klíč": "PaymentID",
			"Třída": "Alstanet.Fm.Ivo.Business.Payment, App_Code",
			"Aplikační objekt": "FM.IVO.Payment",
			"Modul": "Faktury"
		},
		{
			"ID": "7298026",
			"Název CZ": "Typ výkresu",
			"Tabulka": "FM_CAD_PlanType",
			"Primární klíč": "PlanTypeID",
			"Třída": "Alstanet.Fm.Cad.Business.PlanType, App_Code",
			"Aplikační objekt": "FM.CAD.PlanType",
			"Modul": "Vizualizace"
		},
		{
			"ID": "7337731",
			"Název CZ": "Části povrchu stěn",
			"Tabulka": "FM_PSP_WallPart",
			"Primární klíč": "WallPartID",
			"Třída": "Alstanet.Fm.Psp.Business.WallPart, App_Code",
			"Aplikační objekt": "FM.PSP.WallPart",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7340092",
			"Název CZ": "Typ souborové šablony",
			"Tabulka": "AMC_DocumentTemplateType",
			"Primární klíč": "DocumentTemplateTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DocumentTemplateType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DocumentTemplateType",
			"Modul": "Administrace"
		},
		{
			"ID": "7348250",
			"Název CZ": "GPS poloha",
			"Tabulka": "FM_CF_GPSPosition",
			"Primární klíč": "GPSPositionID",
			"Třída": "Alstanet.Fm.Cf.Business.GPSPosition, App_Code",
			"Aplikační objekt": "FM.CF.GPSPosition",
			"Modul": "Autopark"
		},
		{
			"ID": "7375103",
			"Název CZ": "Programová úprava",
			"Tabulka": "AMC_ACEProgramChange",
			"Primární klíč": "ACEProgramChangeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ACEProgramChange, Alstanet.Library.CustEntity",
			"Aplikační objekt": "AMC.ACEProgramChange",
			"Modul": "Administrace"
		},
		{
			"ID": "7377844",
			"Název CZ": "Vzor",
			"Tabulka": "FM_CAD_Pattern",
			"Primární klíč": "PatternID",
			"Třída": "Alstanet.Fm.Cad.Business.Pattern, App_Code",
			"Aplikační objekt": "FM.CAD.Pattern",
			"Modul": "Vizualizace"
		},
		{
			"ID": "7446205",
			"Název CZ": "Typ události",
			"Tabulka": "FM_CF_EventType",
			"Primární klíč": "EventTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.EventType, App_Code",
			"Aplikační objekt": "FM.CF.EventType",
			"Modul": "Autopark"
		},
		{
			"ID": "7472963",
			"Název CZ": "Profilové měření",
			"Tabulka": "FM_ENG_ACEProfileValues",
			"Primární klíč": "ACEProfileValuesID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEProfileValues, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEProfileValues",
			"Modul": "Energie"
		},
		{
			"ID": "7507103",
			"Název CZ": "Vypočtené KPI",
			"Tabulka": "FM_WF_KpiTimeEntity",
			"Primární klíč": "KpiTimeEntityID",
			"Třída": "Alstanet.Fm.Mnt.Business.KpiTimeEntity, App_Code",
			"Aplikační objekt": "FM.MNT.KpiTimeEntity",
			"Modul": "Údržba"
		},
		{
			"ID": "7513070",
			"Název CZ": "Kategorie odpadu",
			"Tabulka": "ACM_WST_ACEWasteCategory",
			"Primární klíč": "ACEWasteCategoryID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteCategory, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteCategory",
			"Modul": "Odpady"
		},
		{
			"ID": "7519186",
			"Název CZ": "Druh podlaží",
			"Tabulka": "FM_PSP_FloorKind",
			"Primární klíč": "FloorKindID",
			"Třída": "Alstanet.Fm.Psp.Business.FloorKind, App_Code",
			"Aplikační objekt": "FM.PSP.FloorKind",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7578065",
			"Název CZ": "Dosažené kritérium",
			"Tabulka": "FM_HR_Employee_Criterion",
			"Primární klíč": "Employee_CriterionID",
			"Třída": "Alstanet.Fm.Hr.Business.Employee_Criterion, App_Code",
			"Aplikační objekt": "FM.HR.Employee_Criterion",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "7583332",
			"Název CZ": "Role subjektu",
			"Tabulka": "FM_HR_SubjectRole",
			"Primární klíč": "SubjectRoleID",
			"Třída": "Alstanet.Fm.Hr.Business.SubjectRole, App_Code",
			"Aplikační objekt": "FM.HR.SubjectRole",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "7597806",
			"Název CZ": "Bonita",
			"Tabulka": "FM_PSP_SiteClass",
			"Primární klíč": "SiteClassID",
			"Třída": "Alstanet.Fm.Psp.Business.SiteClass, App_Code",
			"Aplikační objekt": "FM.PSP.SiteClass",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7603413",
			"Název CZ": "Url",
			"Tabulka": "AMC_EventLogUrl",
			"Primární klíč": "EventLogUrlID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogUrl, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogUrl",
			"Modul": "Administrace"
		},
		{
			"ID": "7616947",
			"Název CZ": "Typ plochy",
			"Tabulka": "FM_PSP_AreaType",
			"Primární klíč": "AreaTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.AreaType, App_Code",
			"Aplikační objekt": "FM.PSP.AreaType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7648332",
			"Název CZ": "Typ schvalovatele",
			"Tabulka": "FM_WF_ApproverType",
			"Primární klíč": "ApproverTypeID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproverType, App_Code",
			"Aplikační objekt": "FM.WF.ApproverType",
			"Modul": "Workflow"
		},
		{
			"ID": "7699833",
			"Název CZ": "Katastrální území",
			"Tabulka": "FM_CDS_Area",
			"Primární klíč": "AreaID",
			"Třída": "Alstanet.Fm.Cds.Business.Area, App_Code",
			"Aplikační objekt": "FM.CDS.Area",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "7718822",
			"Název CZ": "Podkategorie nemovitosti",
			"Tabulka": "FM_PSP_ACESubCategory",
			"Primární klíč": "ACESubCategoryID",
			"Třída": "Alstanet.Fm.Psp.Business.ACESubCategory, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACESubCategory",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7720866",
			"Název CZ": "Povrch podlahy",
			"Tabulka": "FM_PSP_FloorSurface",
			"Primární klíč": "FloorSurfaceID",
			"Třída": "Alstanet.Fm.Psp.Business.FloorSurface, App_Code",
			"Aplikační objekt": "FM.PSP.FloorSurface",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7739622",
			"Název CZ": "Pojištění vozidla",
			"Tabulka": "FM_CF_CarInsurance",
			"Primární klíč": "CarInsuranceID",
			"Třída": "Alstanet.Fm.Cf.Business.CarInsurance, App_Code",
			"Aplikační objekt": "FM.CF.CarInsurance",
			"Modul": "Autopark"
		},
		{
			"ID": "7767391",
			"Název CZ": "Typ parkování",
			"Tabulka": "FM_CF_ParkingType",
			"Primární klíč": "ParkingTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.ParkingType, App_Code",
			"Aplikační objekt": "FM.CF.ParkingType",
			"Modul": "Autopark"
		},
		{
			"ID": "7848009",
			"Název CZ": "Měsíční spotřeba",
			"Tabulka": "FM_ENG_ACEMonthConsumption",
			"Primární klíč": "ACEMonthConsumptionID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEMonthConsumption, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEMonthConsumption",
			"Modul": "Energie"
		},
		{
			"ID": "7863817",
			"Název CZ": "Typ DVR",
			"Tabulka": "FM_TCH_ACEDVRType",
			"Primární klíč": "ACEDVRTypeID",
			"Třída": "Alstanet.Fm.Tch.Business.ACEDVRType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.TCH.ACEDVRType",
			"Modul": "Technologie"
		},
		{
			"ID": "7881473",
			"Název CZ": "Zdroj pro repasportizaci",
			"Tabulka": "FM_CAD_RepasportizationSource",
			"Primární klíč": "RepasportizationSourceID",
			"Třída": "Alstanet.Fm.Cad.Business.RepasportizationSource, App_Code",
			"Aplikační objekt": "FM.CAD.RepasportizationSource",
			"Modul": "Vizualizace"
		},
		{
			"ID": "7898408",
			"Název CZ": "Ulice",
			"Tabulka": "FM_PSP_ACEStreet",
			"Primární klíč": "ACEStreetID",
			"Třída": "Alstanet.Fm.Psp.Business.ACEStreet, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACEStreet",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7901576",
			"Název CZ": "Typ ochrany nemovitosti",
			"Tabulka": "FM_CDS_SecurityType",
			"Primární klíč": "SecurityTypeID",
			"Třída": "Alstanet.Fm.Cds.Business.SecurityType, App_Code",
			"Aplikační objekt": "FM.CDS.SecurityType",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "7916064",
			"Název CZ": "Segment pobočky",
			"Tabulka": "FM_PSP_ACEBranchSegment",
			"Primární klíč": "ACEBranchSegmentID",
			"Třída": "Alstanet.Fm.Psp.Business.ACEBranchSegment, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACEBranchSegment",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "7921808",
			"Název CZ": "Typ entity",
			"Tabulka": "AMC_EntityType",
			"Primární klíč": "EntityTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EntityType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EntityType",
			"Modul": "Administrace"
		},
		{
			"ID": "7939451",
			"Název CZ": "Vypočtené KPI",
			"Tabulka": "FM_WF_KpiTimeEntity",
			"Primární klíč": "KpiTimeEntityID",
			"Třída": "Alstanet.Fm.Wf.Business.KpiTimeEntity, App_Code",
			"Aplikační objekt": "FM.WF.KpiTimeEntity",
			"Modul": "Workflow"
		},
		{
			"ID": "7964995",
			"Název CZ": "Způsob pořízení",
			"Tabulka": "FM_AST_PurchaseType",
			"Primární klíč": "PurchaseTypeID",
			"Třída": "Alstanet.Fm.Ast.Business.PurchaseType, App_Code",
			"Aplikační objekt": "FM.AST.PurchaseType",
			"Modul": "Majetek"
		},
		{
			"ID": "7970274",
			"Název CZ": "Převodka",
			"Tabulka": "FM_AST_Transfer",
			"Primární klíč": "TransferID",
			"Třída": "Alstanet.Fm.Ast.Business.Transfer, App_Code",
			"Aplikační objekt": "FM.AST.Transfer",
			"Modul": "Majetek"
		},
		{
			"ID": "7994382",
			"Název CZ": "Typ akce",
			"Tabulka": "AMC_CustActionType",
			"Primární klíč": "CustActionTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustActionType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustActionType",
			"Modul": "Administrace"
		},
		{
			"ID": "8023053",
			"Název CZ": "Přiřazení GPS lokátoru",
			"Tabulka": "FM_CF_GPSLocatorAssign",
			"Primární klíč": "GPSLocatorAssignID",
			"Třída": "Alstanet.Fm.Cf.Business.GPSLocatorAssign, App_Code",
			"Aplikační objekt": "FM.CF.GPSLocatorAssign",
			"Modul": "Autopark"
		},
		{
			"ID": "8040828",
			"Název CZ": "Původ kurzovního lístku",
			"Tabulka": "FM_FI_ExchangeRateOrigin",
			"Primární klíč": "ExchangeRateOriginID",
			"Třída": "Alstanet.Fm.Fi.Business.ExchangeRateOrigin, App_Code",
			"Aplikační objekt": "FM.FI.ExchangeRateOrigin",
			"Modul": "Ekonomika"
		},
		{
			"ID": "8061449",
			"Název CZ": "Šablona vytěžení dat",
			"Tabulka": "AMC_DataExtractionTemplate",
			"Primární klíč": "DataExtractionTemplateID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DataExtractionTemplate, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DataExtractionTemplate",
			"Modul": "Administrace"
		},
		{
			"ID": "8066483",
			"Název CZ": "Perioda fixních cen",
			"Tabulka": "FM_ENG_FixedPricePeriod",
			"Primární klíč": "FixedPricePeriodID",
			"Třída": "Alstanet.Fm.Eng.Business.FixedPricePeriod, App_Code",
			"Aplikační objekt": "FM.ENG.FixedPricePeriod",
			"Modul": "Energie"
		},
		{
			"ID": "8085872",
			"Název CZ": "Úrověň činnosti",
			"Tabulka": "FM_FI_ActivityDepth",
			"Primární klíč": "ActivityDepthID",
			"Třída": "Alstanet.Fm.Fi.Business.ActivityDepth, App_Code",
			"Aplikační objekt": "FM.FI.ActivityDepth",
			"Modul": "Ekonomika"
		},
		{
			"ID": "8088273",
			"Název CZ": "Typ přílohy",
			"Tabulka": "AMC_AttachmentParentType",
			"Primární klíč": "AttachmentParentTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.AttachmentParentType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.AttachmentParentType",
			"Modul": "Administrace"
		},
		{
			"ID": "8109874",
			"Název CZ": "HTTP log",
			"Tabulka": "AMC_RequestLog",
			"Primární klíč": "RequestLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.RequestLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.RequestLog",
			"Modul": "Administrace"
		},
		{
			"ID": "8148630",
			"Název CZ": "ISPOP hlášení",
			"Tabulka": "ACM_WST_ACEISPOPReport",
			"Primární klíč": "ACEISPOPReportID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEISPOPReport, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEISPOPReport",
			"Modul": "Odpady"
		},
		{
			"ID": "8152000",
			"Název CZ": "Skupina stavů",
			"Tabulka": "FM_WF_StatusGroup",
			"Primární klíč": "StatusGroupID",
			"Třída": "Alstanet.Fm.Wf.Business.StatusGroup, App_Code",
			"Aplikační objekt": "FM.WF.StatusGroup",
			"Modul": "Workflow"
		},
		{
			"ID": "8164941",
			"Název CZ": "Hodnota číselníkového atributu",
			"Tabulka": "AMC_CustPropertyEnum",
			"Primární klíč": "CustPropertyEnumID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustPropertyEnum, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustPropertyEnum",
			"Modul": "Administrace"
		},
		{
			"ID": "8183169",
			"Název CZ": "Resource Log",
			"Tabulka": "AMC_ResourceLog",
			"Primární klíč": "ResourceLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ResourceLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "AMC.ResourceLog",
			"Modul": "Administrace"
		},
		{
			"ID": "8203342",
			"Název CZ": "Hlášení o dopravní nehodě",
			"Tabulka": "FM_CF_IncidentReport",
			"Primární klíč": "IncidentReportID",
			"Třída": "Alstanet.Fm.Cf.Business.IncidentReport, App_Code",
			"Aplikační objekt": "FM.CF.IncidentReport",
			"Modul": "Autopark"
		},
		{
			"ID": "8212702",
			"Název CZ": "Měrná jednotka",
			"Tabulka": "FM_FI_MeasureUnit",
			"Primární klíč": "MeasureUnitID",
			"Třída": "Alstanet.Fm.Fi.Business.MeasureUnit, App_Code",
			"Aplikační objekt": "FM.FI.MeasureUnit",
			"Modul": "Ekonomika"
		},
		{
			"ID": "8220149",
			"Název CZ": "Transakční log",
			"Tabulka": "AMC_TransactionLog",
			"Primární klíč": "TransactionLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.TransactionLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.TransactionLog",
			"Modul": "Administrace"
		},
		{
			"ID": "8227475",
			"Název CZ": "Kapacita",
			"Tabulka": "FM_ENG_Capacity",
			"Primární klíč": "CapacityID",
			"Třída": "Alstanet.Fm.Eng.Business.Capacity, App_Code",
			"Aplikační objekt": "FM.ENG.Capacity",
			"Modul": "Energie"
		},
		{
			"ID": "8242951",
			"Název CZ": "Forma úhrady",
			"Tabulka": "FM_IVO_PaymentForm",
			"Primární klíč": "PaymentFormID",
			"Třída": "Alstanet.Fm.Ivo.Business.PaymentForm, App_Code",
			"Aplikační objekt": "FM.IVO.PaymentForm",
			"Modul": "Faktury"
		},
		{
			"ID": "8248376",
			"Název CZ": "Šablona checklistu",
			"Tabulka": "FM_MNT_Checklist",
			"Primární klíč": "ChecklistID",
			"Třída": "Alstanet.Fm.Mnt.Business.Checklist, App_Code",
			"Aplikační objekt": "FM.MNT.Checklist",
			"Modul": "Údržba"
		},
		{
			"ID": "8262867",
			"Název CZ": "Pokuta",
			"Tabulka": "FM_CF_Fine",
			"Primární klíč": "FineID",
			"Třída": "Alstanet.Fm.Cf.Business.Fine, App_Code",
			"Aplikační objekt": "FM.CF.Fine",
			"Modul": "Autopark"
		},
		{
			"ID": "8264165",
			"Název CZ": "Oblast smlouvy",
			"Tabulka": "FM_CM_ACEContractArea",
			"Primární klíč": "ACEContractAreaID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEContractArea, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEContractArea",
			"Modul": "Smlouvy"
		},
		{
			"ID": "8271683",
			"Název CZ": "Detail logu přechodu",
			"Tabulka": "FM_WF_StatusLogItem",
			"Primární klíč": "StatusLogItemID",
			"Třída": "Alstanet.Fm.Wf.Business.StatusLogItem, App_Code",
			"Aplikační objekt": "FM.WF.StatusLogItem",
			"Modul": "Workflow"
		},
		{
			"ID": "8316978",
			"Název CZ": "Vazba dokumentu",
			"Tabulka": "FM_DMS_Document_Entity",
			"Primární klíč": "Document_EntityID",
			"Třída": "Alstanet.Fm.Dms.Business.Document_Entity, App_Code",
			"Aplikační objekt": "FM.DMS.DocumentEntity",
			"Modul": "DMS"
		},
		{
			"ID": "8376693",
			"Název CZ": "Typ modelu",
			"Tabulka": "FM_CF_ModelType",
			"Primární klíč": "ModelTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.ModelType, App_Code",
			"Aplikační objekt": "",
			"Modul": "Autopark"
		},
		{
			"ID": "8415551",
			"Název CZ": "Nový majetek",
			"Tabulka": "FM_INV_NewAsset",
			"Primární klíč": "NewAssetID",
			"Třída": "Alstanet.Fm.Inv.Business.NewAsset, App_Code",
			"Aplikační objekt": "FM.INV.NewAsset",
			"Modul": "Inventury"
		},
		{
			"ID": "8419664",
			"Název CZ": "Skupina zdroje",
			"Tabulka": "FM_FI_SourceRootType",
			"Primární klíč": "SourceRootTypeID",
			"Třída": "Alstanet.Fm.Fi.Business.SourceRootType, App_Code",
			"Aplikační objekt": "FM.FI.SourceRootType",
			"Modul": "Ekonomika"
		},
		{
			"ID": "8422027",
			"Název CZ": "Způsob využití jednotky",
			"Tabulka": "FM_CDS_UnitGroupUsage",
			"Primární klíč": "UnitGroupUsageID",
			"Třída": "Alstanet.Fm.Cds.Business.UnitGroupUsage, App_Code",
			"Aplikační objekt": "FM.CDS.UnitGroupUsage",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "8431286",
			"Název CZ": "Označení zboží",
			"Tabulka": "FM_CF_GoodsIdentification",
			"Primární klíč": "GoodsIdentificationID",
			"Třída": "Alstanet.Fm.Cf.Business.GoodsIdentification, App_Code",
			"Aplikační objekt": "FM.CF.GoodsIdentification",
			"Modul": "Autopark"
		},
		{
			"ID": "8450714",
			"Název CZ": "Typ dokumentu smlouvy",
			"Tabulka": "FM_CM_ACEContractDocumentType",
			"Primární klíč": "ACEContractDocumentTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.ACEContractDocumentType, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CM.ACEContractDocumentType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "8462596",
			"Název CZ": "Pravidelný úklid - položka",
			"Tabulka": "FM_PSP_CleaningPlanItem",
			"Primární klíč": "CleaningPlanItemID",
			"Třída": "Alstanet.Fm.Psp.Business.CleaningPlanItem, App_Code",
			"Aplikační objekt": "FM.PSP.CleaningPlanItem",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "8464514",
			"Název CZ": "Mapování entity na typ výkresu",
			"Tabulka": "FM_CAD_PlanTypeEntityMapping",
			"Primární klíč": "PlanTypeEntityMappingID",
			"Třída": "Alstanet.Fm.Cad.Business.PlanTypeEntityMapping, App_Code",
			"Aplikační objekt": "FM.CAD.PlanTypeEntityMapping",
			"Modul": "Vizualizace"
		},
		{
			"ID": "8489842",
			"Název CZ": "Způsob indexace",
			"Tabulka": "FM_CM_PriceIndexMethod",
			"Primární klíč": "PriceIndexMethodID",
			"Třída": "Alstanet.Fm.Cm.Business.PriceIndexMethod, App_Code",
			"Aplikační objekt": "FM.CM.PriceIndexMethod",
			"Modul": "Smlouvy"
		},
		{
			"ID": "8496583",
			"Název CZ": "Typ zdroje",
			"Tabulka": "FM_FI_SourceType",
			"Primární klíč": "SourceTypeID",
			"Třída": "Alstanet.Fm.Fi.Business.SourceType, App_Code",
			"Aplikační objekt": "FM.FI.SourceType",
			"Modul": "Ekonomika"
		},
		{
			"ID": "8527450",
			"Název CZ": "Číselná řada",
			"Tabulka": "AMC_NumberSeries",
			"Primární klíč": "NumberSeriesID",
			"Třída": "Alstanet.Library.Web.AMC.Business.NumberSeries, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.NumberSeries",
			"Modul": "Administrace"
		},
		{
			"ID": "8563218",
			"Název CZ": "Typ plochy",
			"Tabulka": "FM_PSP_RoomType",
			"Primární klíč": "RoomTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.RoomType, App_Code",
			"Aplikační objekt": "FM.PSP.RoomType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "8575632",
			"Název CZ": "Přidělení pracovního místa",
			"Tabulka": "FM_HR_Employee_Workspace",
			"Primární klíč": "Employee_WorkspaceID",
			"Třída": "Alstanet.Fm.Hr.Business.Employee_Workspace, App_Code",
			"Aplikační objekt": "FM.HR.Employee_Workspace",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "8595449",
			"Název CZ": "Kapitola odpadu",
			"Tabulka": "ACM_WST_ACEWasteChapter",
			"Primární klíč": "ACEWasteChapterID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteChapter, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteChapter",
			"Modul": "Odpady"
		},
		{
			"ID": "8605994",
			"Název CZ": "Položka vybavení vozidla",
			"Tabulka": "FM_CF_CarEquipmentItem",
			"Primární klíč": "CarEquipmentItemID",
			"Třída": "Alstanet.Fm.Cf.Business.CarEquipmentItem, App_Code",
			"Aplikační objekt": "FM.CF.CarEquipmentItem",
			"Modul": "Autopark"
		},
		{
			"ID": "8617877",
			"Název CZ": "Měsíční vyúčtování",
			"Tabulka": "FM_MNT_MonthlyClosing",
			"Primární klíč": "MonthlyClosingID",
			"Třída": "Alstanet.Fm.Mnt.Business.MonthlyClosing, App_Code",
			"Aplikační objekt": "FM.MNT.MonthlyClosing",
			"Modul": "Údržba"
		},
		{
			"ID": "8654063",
			"Název CZ": "Typ kultivace",
			"Tabulka": "FM_CAD_CultivationType",
			"Primární klíč": "CultivationTypeID",
			"Třída": "Alstanet.Fm.Cad.Business.CultivationType, App_Code",
			"Aplikační objekt": "FM.CAD.CultivationType",
			"Modul": "Vizualizace"
		},
		{
			"ID": "8668097",
			"Název CZ": "Branch building strategy",
			"Tabulka": "FM_PSP_BranchBuildingStrategy",
			"Primární klíč": "BranchBuildingStrategyID",
			"Třída": "Alstanet.Fm.Psp.Business.BranchBuildingStrategy, App_Code",
			"Aplikační objekt": "FM.PSP.BranchBuildingStrategy",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "8700135",
			"Název CZ": "Pobídka",
			"Tabulka": "FM_CM_Incentive",
			"Primární klíč": "IncentiveID",
			"Třída": "Alstanet.Fm.Cm.Business.Incentive, App_Code",
			"Aplikační objekt": "FM.CM.Incentive",
			"Modul": "Smlouvy"
		},
		{
			"ID": "8725237",
			"Název CZ": "ESRS data point",
			"Tabulka": "ACM_ESG_ACEESRSDataPoint",
			"Primární klíč": "ACEESRSDataPointID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEESRSDataPoint, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEESRSDataPoint",
			"Modul": "ESG"
		},
		{
			"ID": "8730176",
			"Název CZ": "IP",
			"Tabulka": "AMC_EventLogIP",
			"Primární klíč": "EventLogIPID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogIP, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogIP",
			"Modul": "Administrace"
		},
		{
			"ID": "8750845",
			"Název CZ": "Zboží",
			"Tabulka": "FM_CF_Goods",
			"Primární klíč": "GoodsID",
			"Třída": "Alstanet.Fm.Cf.Business.Goods, App_Code",
			"Aplikační objekt": "FM.CF.Goods",
			"Modul": "Autopark"
		},
		{
			"ID": "8753549",
			"Název CZ": "Bilance odpadu",
			"Tabulka": "ACM_WST_ACEWasteBalance",
			"Primární klíč": "ACEWasteBalanceID",
			"Třída": "Alstanet.Acm.Wst.Business.ACEWasteBalance, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.WST.ACEWasteBalance",
			"Modul": "Odpady"
		},
		{
			"ID": "8799495",
			"Název CZ": "Číslo účtu",
			"Tabulka": "FM_FI_SyntheticAccount",
			"Primární klíč": "SyntheticAccountID",
			"Třída": "Alstanet.Fm.Fi.Business.SyntheticAccount, App_Code",
			"Aplikační objekt": "FM.FI.SyntheticAccount",
			"Modul": "Ekonomika"
		},
		{
			"ID": "8808562",
			"Název CZ": "Typ customizace",
			"Tabulka": "AMC_CustType",
			"Primární klíč": "CustTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustType",
			"Modul": "Administrace"
		},
		{
			"ID": "8841710",
			"Název CZ": "Vazba složky",
			"Tabulka": "FM_DMS_Folder_Entity",
			"Primární klíč": "Folder_EntityID",
			"Třída": "Alstanet.Fm.Dms.Business.Folder_Entity, App_Code",
			"Aplikační objekt": "FM.DMS.FolderEntity",
			"Modul": "DMS"
		},
		{
			"ID": "8874141",
			"Název CZ": "Karta na čtečce",
			"Tabulka": "FM_KCM_CardReader_Card",
			"Primární klíč": "CardReader_CardID",
			"Třída": "Alstanet.Fm.Kcm.Business.CardReader_Card, App_Code",
			"Aplikační objekt": "FM.KCM.CardReader_Card",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "8883500",
			"Název CZ": "Typ obratu",
			"Tabulka": "FM_CM_TurnoverType",
			"Primární klíč": "TurnoverTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.TurnoverType, App_Code",
			"Aplikační objekt": "FM.CM.TurnoverType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "8918384",
			"Název CZ": "Jízda",
			"Tabulka": "FM_CF_DrivebookItem",
			"Primární klíč": "DrivebookItemID",
			"Třída": "Alstanet.Fm.Cf.Business.DrivebookItem, App_Code",
			"Aplikační objekt": "FM.CF.DrivebookItem",
			"Modul": "Autopark"
		},
		{
			"ID": "8933489",
			"Název CZ": "Podíl",
			"Tabulka": "FM_IVO_DistributionKeyShare",
			"Primární klíč": "DistributionKeyShareID",
			"Třída": "Alstanet.Fm.Ivo.Business.DistributionKeyShare, App_Code",
			"Aplikační objekt": "FM.IVO.DistributionKeyShare",
			"Modul": "Faktury"
		},
		{
			"ID": "9007937",
			"Název CZ": "Ceník sazeb",
			"Tabulka": "FM_ENG_RatePrice",
			"Primární klíč": "RatePriceID",
			"Třída": "Alstanet.Fm.Eng.Business.RatePrice, App_Code",
			"Aplikační objekt": "FM.ENG.RatePrice",
			"Modul": "Energie"
		},
		{
			"ID": "9012640",
			"Název CZ": "Dokument",
			"Tabulka": "FM_DMS_Document",
			"Primární klíč": "DocumentID",
			"Třída": "Alstanet.Fm.Dms.Business.Document, App_Code",
			"Aplikační objekt": "FM.DMS.Document",
			"Modul": "DMS"
		},
		{
			"ID": "9015042",
			"Název CZ": "Spojení",
			"Tabulka": "AMC_Connection",
			"Primární klíč": "ConnectionID",
			"Třída": "Alstanet.Library.Web.AMC.Business.Connection, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.Connection",
			"Modul": "Administrace"
		},
		{
			"ID": "9015359",
			"Název CZ": "Refakturace",
			"Tabulka": "FM_IVO_ReinvoiceSetup",
			"Primární klíč": "ReinvoiceSetupID",
			"Třída": "Alstanet.Fm.Ivo.Business.ReinvoiceSetup, App_Code",
			"Aplikační objekt": "FM.IVO.ReinvoiceSetup",
			"Modul": "Faktury"
		},
		{
			"ID": "9039799",
			"Název CZ": "Název místnosti",
			"Tabulka": "FM_PSP_RoomSubType",
			"Primární klíč": "RoomSubTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.RoomSubType, App_Code",
			"Aplikační objekt": "FM.PSP.RoomSubType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "9050992",
			"Název CZ": "RZ vozidla",
			"Tabulka": "FM_CF_CarSPZ",
			"Primární klíč": "CarSPZID",
			"Třída": "Alstanet.Fm.Cf.Business.CarSPZ, App_Code",
			"Aplikační objekt": "FM.CF.CarSPZ",
			"Modul": "Autopark"
		},
		{
			"ID": "9054483",
			"Název CZ": "Subjekt z ARESu",
			"Tabulka": "",
			"Primární klíč": "",
			"Třída": "Alstanet.Fm.Hr.Business.ARES_Subject, App_Code",
			"Aplikační objekt": "",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "9065381",
			"Název CZ": "Obrázek",
			"Tabulka": "FM_CMS_ArticlePicture",
			"Primární klíč": "ArticlePictureID",
			"Třída": "Alstanet.Fm.Cms.Business.ArticlePicture, App_Code",
			"Aplikační objekt": "FM.CMS.ArticlePicture",
			"Modul": "Portál"
		},
		{
			"ID": "9070400",
			"Název CZ": "Denní opakování",
			"Tabulka": "AMC_ScheduleFreqSubdayType",
			"Primární klíč": "ScheduleFreqSubdayTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.ScheduleFreqSubdayType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.ScheduleFreqSubdayType",
			"Modul": "Administrace"
		},
		{
			"ID": "9082068",
			"Název CZ": "Skupina jednotek",
			"Tabulka": "FM_FI_MeasureUnitGroup",
			"Primární klíč": "MeasureUnitGroupID",
			"Třída": "Alstanet.Fm.Fi.Business.MeasureUnitGroup, App_Code",
			"Aplikační objekt": "FM.FI.MeasureUnitGroup",
			"Modul": "Ekonomika"
		},
		{
			"ID": "9111014",
			"Název CZ": "Entita ve zprávě",
			"Tabulka": "AMC_MessageLog_Entity",
			"Primární klíč": "MessageLog_EntityID",
			"Třída": "Alstanet.Library.Web.AMC.Business.MessageLog_Entity, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.MessageLog_Entity",
			"Modul": "Administrace"
		},
		{
			"ID": "9125462",
			"Název CZ": "Kategorie logu událostí",
			"Tabulka": "AMC_EventLogCategory",
			"Primární klíč": "EventLogCategoryID",
			"Třída": "Alstanet.Library.Web.AMC.Business.EventLogCategory, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.EventLogCategory",
			"Modul": "Administrace"
		},
		{
			"ID": "9134730",
			"Název CZ": "Stav inventarizovaného majetku",
			"Tabulka": "FM_INV_AssetStatus",
			"Primární klíč": "AssetStatusID",
			"Třída": "Alstanet.Fm.Inv.Business.AssetStatus, App_Code",
			"Aplikační objekt": "FM.INV.AssetStatus",
			"Modul": "Inventury"
		},
		{
			"ID": "9144454",
			"Název CZ": "Forma vlastnictví",
			"Tabulka": "FM_CF_OwnershipType",
			"Primární klíč": "OwnershipTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.OwnershipType, App_Code",
			"Aplikační objekt": "FM.CF.OwnershipType",
			"Modul": "Autopark"
		},
		{
			"ID": "9156807",
			"Název CZ": "API Endpoint",
			"Tabulka": "AMC_DataSource",
			"Primární klíč": "DataSourceID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DataSource, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DataSource",
			"Modul": "Administrace"
		},
		{
			"ID": "9209937",
			"Název CZ": "Rozlišení parcel",
			"Tabulka": "FM_CDS_ParcelEvidenceType",
			"Primární klíč": "ParcelEvidenceTypeID",
			"Třída": "Alstanet.Fm.Cds.Business.ParcelEvidenceType, App_Code",
			"Aplikační objekt": "FM.CDS.ParcelEvidenceType",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "9213754",
			"Název CZ": "Distribuční území",
			"Tabulka": "FM_CF_DistributionArea",
			"Primární klíč": "DistributionAreaID",
			"Třída": "Alstanet.Fm.Cf.Business.DistributionArea, App_Code",
			"Aplikační objekt": "FM.CF.DistributionArea",
			"Modul": "Autopark"
		},
		{
			"ID": "9223981",
			"Název CZ": "Přidělení parkovací karty",
			"Tabulka": "FM_CF_ParkCardAssign",
			"Primární klíč": "ParkCardAssignID",
			"Třída": "Alstanet.Fm.Cf.Business.ParkCardAssign, App_Code",
			"Aplikační objekt": "FM.CF.ParkCardAssign",
			"Modul": "Autopark"
		},
		{
			"ID": "9226145",
			"Název CZ": "Karta",
			"Tabulka": "FM_CF_Card",
			"Primární klíč": "CardID",
			"Třída": "Alstanet.Fm.Cf.Business.Card, App_Code",
			"Aplikační objekt": "FM.CF.Card",
			"Modul": "Autopark"
		},
		{
			"ID": "9228308",
			"Název CZ": "Venkovní teplota",
			"Tabulka": "FM_ENG_Temperature",
			"Primární klíč": "TemperatureID",
			"Třída": "Alstanet.Fm.Eng.Business.Temperature, App_Code",
			"Aplikační objekt": "FM.ENG.Temperature",
			"Modul": "Energie"
		},
		{
			"ID": "9237869",
			"Název CZ": "Typ tokenu",
			"Tabulka": "AMC_TokenType",
			"Primární klíč": "TokenTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.TokenType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.TokenType",
			"Modul": "Administrace"
		},
		{
			"ID": "9245297",
			"Název CZ": "Typ odložení",
			"Tabulka": "FM_MNT_PostponeType",
			"Primární klíč": "PostponeTypeID",
			"Třída": "Alstanet.Fm.Mnt.Business.PostponeType, App_Code",
			"Aplikační objekt": "FM.MNT.PostponeType",
			"Modul": "Údržba"
		},
		{
			"ID": "9248007",
			"Název CZ": "Typ pracovního místa",
			"Tabulka": "FM_PSP_WorkspaceType",
			"Primární klíč": "WorkspaceTypeID",
			"Třída": "Alstanet.Fm.Psp.Business.WorkspaceType, App_Code",
			"Aplikační objekt": "FM.PSP.WorkspaceType",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "9249623",
			"Název CZ": "Položka checklistu na entitě",
			"Tabulka": "FM_MNT_Checklist_Entity",
			"Primární klíč": "Checklist_EntityID",
			"Třída": "Alstanet.Fm.Mnt.Business.Checklist_Entity, App_Code",
			"Aplikační objekt": "FM.MNT.Checklist_Entity",
			"Modul": "Údržba"
		},
		{
			"ID": "9254041",
			"Název CZ": "Doplňková platba",
			"Tabulka": "FM_CF_AdditionalPayment",
			"Primární klíč": "AdditionalPaymentID",
			"Třída": "Alstanet.Fm.Cf.Business.AdditionalPayment, App_Code",
			"Aplikační objekt": "FM.CF.AdditionalPayment",
			"Modul": "Autopark"
		},
		{
			"ID": "9254832",
			"Název CZ": "Druh číslování parcel",
			"Tabulka": "FM_CDS_ParcelNumberingType",
			"Primární klíč": "ParcelNumberingTypeID",
			"Třída": "Alstanet.Fm.Cds.Business.ParcelNumberingType, App_Code",
			"Aplikační objekt": "FM.CDS.ParcelNumberingType",
			"Modul": "Majetkoprávní vztahy"
		},
		{
			"ID": "9267325",
			"Název CZ": "Roční spotřeba elektřiny",
			"Tabulka": "FM_ENG_ACEElectricityYearConsumption",
			"Primární klíč": "ACEElectricityYearConsumptionID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEElectricityYearConsumption, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEElectricityYearConsumption",
			"Modul": "Energie"
		},
		{
			"ID": "9303134",
			"Název CZ": "Typ opce",
			"Tabulka": "FM_CM_OptionType",
			"Primární klíč": "OptionTypeID",
			"Třída": "Alstanet.Fm.Cm.Business.OptionType, App_Code",
			"Aplikační objekt": "FM.CM.OptionType",
			"Modul": "Smlouvy"
		},
		{
			"ID": "9306765",
			"Název CZ": "Duplicita požadavku",
			"Tabulka": "FM_MNT_IncidentDuplicate",
			"Primární klíč": "IncidentDuplicateID",
			"Třída": "Alstanet.Fm.Mnt.Business.IncidentDuplicate, App_Code",
			"Aplikační objekt": "FM.MNT.IncidentDuplicate",
			"Modul": "Údržba"
		},
		{
			"ID": "9331857",
			"Název CZ": "Kód krácení odpočtu",
			"Tabulka": "FM_FI_DPHCurtailmentCode",
			"Primární klíč": "DPHCurtailmentCodeID",
			"Třída": "Alstanet.Fm.Fi.Business.DPHCurtailmentCode, App_Code",
			"Aplikační objekt": "FM.FI.DPHCurtailmentCode",
			"Modul": "Ekonomika"
		},
		{
			"ID": "9332648",
			"Název CZ": "ESRS report položka",
			"Tabulka": "ACM_ESG_ACEESRSReportItem",
			"Primární klíč": "ACEESRSReportItemID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEESRSReportItem, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEESRSReportItem",
			"Modul": "ESG"
		},
		{
			"ID": "9343975",
			"Název CZ": "Šablona smlouvy",
			"Tabulka": "FM_CM_ContractTemplate",
			"Primární klíč": "ContractTemplateID",
			"Třída": "Alstanet.Fm.Cm.Business.ContractTemplate, App_Code",
			"Aplikační objekt": "FM.CM.ContractTemplate",
			"Modul": "Smlouvy"
		},
		{
			"ID": "9367106",
			"Název CZ": "Stav osoby",
			"Tabulka": "FM_HR_EmployeeStatus",
			"Primární klíč": "EmployeeStatusID",
			"Třída": "Alstanet.Fm.Hr.Business.EmployeeStatus, App_Code",
			"Aplikační objekt": "FM.HR.EmployeeStatus",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "9370174",
			"Název CZ": "Karoserie",
			"Tabulka": "FM_CF_ACECarbody",
			"Primární klíč": "ACECarbodyID",
			"Třída": "Alstanet.Fm.Cf.Business.ACECarbody, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CF.ACECarbody",
			"Modul": "Autopark"
		},
		{
			"ID": "9410227",
			"Název CZ": "ESRS kapitola",
			"Tabulka": "ACM_ESG_ACEESRSChapter",
			"Primární klíč": "ACEESRSChapterID",
			"Třída": "Alstanet.Acm.Esg.Business.ACEESRSChapter, Alstanet.Library.CustEntity",
			"Aplikační objekt": "ACM.ESG.ACEESRSChapter",
			"Modul": "ESG"
		},
		{
			"ID": "9427789",
			"Název CZ": "Kompetentní oblast",
			"Tabulka": "FM_PSP_ACECompetentArea",
			"Primární klíč": "ACECompetentAreaID",
			"Třída": "Alstanet.Fm.Psp.Business.ACECompetentArea, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.PSP.ACECompetentArea",
			"Modul": "Nemovitosti"
		},
		{
			"ID": "9450057",
			"Název CZ": "Kritérium",
			"Tabulka": "FM_HR_Criterion",
			"Primární klíč": "CriterionID",
			"Třída": "Alstanet.Fm.Hr.Business.Criterion, App_Code",
			"Aplikační objekt": "FM.HR.Criterion",
			"Modul": "Subjekty a osoby"
		},
		{
			"ID": "9462543",
			"Název CZ": "KPI",
			"Tabulka": "FM_WF_KpiTime",
			"Primární klíč": "KpiTimeID",
			"Třída": "Alstanet.Fm.Wf.Business.KpiTime, App_Code",
			"Aplikační objekt": "FM.WF.KpiTime",
			"Modul": "Workflow"
		},
		{
			"ID": "9468870",
			"Název CZ": "Grafický objekt",
			"Tabulka": "FM_CAD_GraphicsObject",
			"Primární klíč": "GraphicsObjectID",
			"Třída": "Alstanet.Fm.Cad.Business.GraphicsObject, App_Code",
			"Aplikační objekt": "FM.CAD.GraphicsObject",
			"Modul": "Vizualizace"
		},
		{
			"ID": "9474667",
			"Název CZ": "Kvartální spotřeba",
			"Tabulka": "FM_ENG_ACEQuartalConsumption",
			"Primární klíč": "ACEQuartalConsumptionID",
			"Třída": "Alstanet.Fm.Eng.Business.ACEQuartalConsumption, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.ENG.ACEQuartalConsumption",
			"Modul": "Energie"
		},
		{
			"ID": "9528695",
			"Název CZ": "Zpráva",
			"Tabulka": "FM_WF_Message",
			"Primární klíč": "MessageID",
			"Třída": "Alstanet.Fm.Wf.Business.Message, App_Code",
			"Aplikační objekt": "FM.WF.Message",
			"Modul": "Workflow"
		},
		{
			"ID": "9532559",
			"Název CZ": "Zpracovatel API Endpointu",
			"Tabulka": "AMC_DataSourceProcesser",
			"Primární klíč": "DataSourceProcesserID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DataSourceProcesser, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DataSourceProcesser",
			"Modul": "Administrace"
		},
		{
			"ID": "9615236",
			"Název CZ": "Přidělení Dálniční známky",
			"Tabulka": "FM_CF_TollStickerAssign",
			"Primární klíč": "TollStickerAssignID",
			"Třída": "Alstanet.Fm.Cf.Business.TollStickerAssign, App_Code",
			"Aplikační objekt": "FM.CF.TollStickerAssign",
			"Modul": "Autopark"
		},
		{
			"ID": "9718359",
			"Název CZ": "Nabídka",
			"Tabulka": "FM_ORD_Offer",
			"Primární klíč": "OfferID",
			"Třída": "Alstanet.Fm.Ord.Business.Offer, App_Code",
			"Aplikační objekt": "FM.ORD.Offer",
			"Modul": "Objednávky"
		},
		{
			"ID": "9718711",
			"Název CZ": "Typ systémového objektu",
			"Tabulka": "AMC_SystemObjectType",
			"Primární klíč": "SystemObjectTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.SystemObjectType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.SystemObjectType",
			"Modul": "Administrace"
		},
		{
			"ID": "9729887",
			"Název CZ": "Ztráty odběrného místa",
			"Tabulka": "FM_ENG_PlaceLoss",
			"Primární klíč": "PlaceLossID",
			"Třída": "Alstanet.Fm.Eng.Business.PlaceLoss, App_Code",
			"Aplikační objekt": "FM.ENG.PlaceLoss",
			"Modul": "Energie"
		},
		{
			"ID": "9739901",
			"Název CZ": "Typ výpočtu ceny PHM",
			"Tabulka": "FM_CF_FuelUnitPriceType",
			"Primární klíč": "FuelUnitPriceTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.FuelUnitPriceType, App_Code",
			"Aplikační objekt": "FM.CF.FuelUnitPriceType",
			"Modul": "Autopark"
		},
		{
			"ID": "9756771",
			"Název CZ": "Vložka zámku",
			"Tabulka": "FM_KCM_LockInsert",
			"Primární klíč": "LockInsertID",
			"Třída": "Alstanet.Fm.Kcm.Business.LockInsert, App_Code",
			"Aplikační objekt": "FM.KCM.LockInsert",
			"Modul": "Klíče a karty"
		},
		{
			"ID": "9773080",
			"Název CZ": "Mýtná událost",
			"Tabulka": "FM_CF_CardTollEvent",
			"Primární klíč": "CardTollEventID",
			"Třída": "Alstanet.Fm.Cf.Business.CardTollEvent, App_Code",
			"Aplikační objekt": "FM.CF.CardTollEvent",
			"Modul": "Autopark"
		},
		{
			"ID": "9783529",
			"Název CZ": "Kategorie užití",
			"Tabulka": "FM_CF_ACEUseCategory",
			"Primární klíč": "ACEUseCategoryID",
			"Třída": "Alstanet.Fm.Cf.Business.ACEUseCategory, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.CF.ACEUseCategory",
			"Modul": "Autopark"
		},
		{
			"ID": "9798790",
			"Název CZ": "Rezervovaná kapacita",
			"Tabulka": "FM_ENG_ReservedCapacity",
			"Primární klíč": "ReservedCapacityID",
			"Třída": "Alstanet.Fm.Eng.Business.ReservedCapacity, App_Code",
			"Aplikační objekt": "FM.ENG.ReservedCapacity",
			"Modul": "Energie"
		},
		{
			"ID": "9814018",
			"Název CZ": "Typ zadávání nájezdu",
			"Tabulka": "FM_CF_FillingRaidType",
			"Primární klíč": "FillingRaidTypeID",
			"Třída": "Alstanet.Fm.Cf.Business.FillingRaidType, App_Code",
			"Aplikační objekt": "FM.CF.FillingRaidType",
			"Modul": "Autopark"
		},
		{
			"ID": "9824445",
			"Název CZ": "Spuštění akce",
			"Tabulka": "AMC_CustActionHierarchy",
			"Primární klíč": "CustActionHierarchyID",
			"Třída": "Alstanet.Library.Web.AMC.Business.CustActionHierarchy, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.CustActionHierarchy",
			"Modul": "Administrace"
		},
		{
			"ID": "9837809",
			"Název CZ": "Povolené zboží",
			"Tabulka": "FM_CF_GoodsAllowed",
			"Primární klíč": "GoodsAllowedID",
			"Třída": "Alstanet.Fm.Cf.Business.GoodsAllowed, App_Code",
			"Aplikační objekt": "FM.CF.GoodsAllowed",
			"Modul": "Autopark"
		},
		{
			"ID": "9850730",
			"Název CZ": "Převáděný majetek",
			"Tabulka": "FM_AST_TransferItem",
			"Primární klíč": "TransferItemID",
			"Třída": "Alstanet.Fm.Ast.Business.TransferItem, App_Code",
			"Aplikační objekt": "FM.AST.TransferItem",
			"Modul": "Majetek"
		},
		{
			"ID": "9857818",
			"Název CZ": "Rozpad CR360",
			"Tabulka": "FM_IVO_ACEInvoiceItemCR360",
			"Primární klíč": "ACEInvoiceItemCR360ID",
			"Třída": "Alstanet.Fm.Ivo.Business.ACEInvoiceItemCR360, Alstanet.Library.CustEntity",
			"Aplikační objekt": "FM.IVO.ACEInvoiceItemCR360",
			"Modul": "Faktury"
		},
		{
			"ID": "9861831",
			"Název CZ": "Šablona schvalování",
			"Tabulka": "FM_WF_ApproveTemplate",
			"Primární klíč": "ApproveTemplateID",
			"Třída": "Alstanet.Fm.Wf.Business.ApproveTemplate, App_Code",
			"Aplikační objekt": "FM.WF.ApproveTemplate",
			"Modul": "Workflow"
		},
		{
			"ID": "9875645",
			"Název CZ": "Typ bezpečnostního objektu",
			"Tabulka": "AMC_SecurityObjectType",
			"Primární klíč": "SecurityObjectTypeID",
			"Třída": "Alstanet.Library.Web.AMC.Business.SecurityObjectType, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.SecurityObjectType",
			"Modul": "Administrace"
		},
		{
			"ID": "9881551",
			"Název CZ": "Datová transformace",
			"Tabulka": "AMC_DataTransform",
			"Primární klíč": "DataTransformID",
			"Třída": "Alstanet.Library.Web.AMC.Business.DataTransform, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.DataTransform",
			"Modul": "Administrace"
		},
		{
			"ID": "9900376",
			"Název CZ": "Přístup k atributu",
			"Tabulka": "AMC_AccessProperty",
			"Primární klíč": "AccessPropertyID",
			"Třída": "Alstanet.Library.Web.AMC.Business.AccessProperty, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.AccessProperty",
			"Modul": "Administrace"
		},
		{
			"ID": "9917749",
			"Název CZ": "Položka činnosti",
			"Tabulka": "FM_FI_ActivityItem",
			"Primární klíč": "ActivityItemID",
			"Třída": "Alstanet.Fm.Fi.Business.ActivityItem, App_Code",
			"Aplikační objekt": "FM.FI.ActivityItem",
			"Modul": "Ekonomika"
		},
		{
			"ID": "9935681",
			"Název CZ": "Link",
			"Tabulka": "FM_DMS_Link",
			"Primární klíč": "LinkID",
			"Třída": "Alstanet.Fm.Dms.Business.Link, App_Code",
			"Aplikační objekt": "FM.DMS.Link",
			"Modul": "DMS"
		},
		{
			"ID": "9972555",
			"Název CZ": "Důvod vyřazení",
			"Tabulka": "FM_AST_LiquidationReason",
			"Primární klíč": "LiquidationReasonID",
			"Třída": "Alstanet.Fm.Ast.Business.LiquidationReason, App_Code",
			"Aplikační objekt": "FM.AST.LiquidationReason",
			"Modul": "Majetek"
		},
		{
			"ID": "9983125",
			"Název CZ": "Náklad na vozidlo",
			"Tabulka": "FM_CF_CarEvent",
			"Primární klíč": "CarEventID",
			"Třída": "Alstanet.Fm.Cf.Business.CarEvent, App_Code",
			"Aplikační objekt": "FM.CF.CarEvent",
			"Modul": "Autopark"
		},
		{
			"ID": "1000000001",
			"Název CZ": "Zpráva",
			"Tabulka": "AMC_MessageLog",
			"Primární klíč": "MessageLogID",
			"Třída": "Alstanet.Library.Web.AMC.Business.MessageLog, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "Admin.MessageLog",
			"Modul": "Administrace"
		},
		{
			"ID": "1000000101",
			"Název CZ": "AMC - statické atributy",
			"Tabulka": "",
			"Primární klíč": "",
			"Třída": "Alstanet.Library.Web.AMC.Business.AmcStaticValue, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "",
			"Modul": "Administrace"
		},
		{
			"ID": "1000000601",
			"Název CZ": "Kontejner datové hodnoty",
			"Tabulka": "",
			"Primární klíč": "",
			"Třída": "Alstanet.Library.Web.AMC.Business.ContainerNode, Alstanet.Library.Web.AMC",
			"Aplikační objekt": "",
			"Modul": "Administrace"
		},
]